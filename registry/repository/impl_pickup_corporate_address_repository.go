package repository

import (
	"github.com/Lionparcel/horde/src/repository"
	"sync"
)

func (r repositoryRegistry) PickupCorporateAddressRepository() repository.PickupCorporateAddressRepository {
	var once sync.Once
	var pickupCorporateAddressRepo repository.PickupCorporateAddressRepository

	once.Do(func() {
		pickupCorporateAddressRepo = repository.NewPickupCorporateAddressRepo(&r.cfg)
	})

	return pickupCorporateAddressRepo
}
