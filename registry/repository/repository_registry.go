package repository

import (
	"sync"

	"github.com/Lionparcel/horde/config/firebase"

	"github.com/Lionparcel/horde/config"
	"github.com/Lionparcel/horde/src/repository"
)

// RepositoryRegistry ...
type RepositoryRegistry interface {
	Account() repository.AccountRepository
	ActivityLog() repository.ActivityLogRepository
	Role() repository.RoleRepository
	Bulk() repository.BulkRepository
	AccountToken() repository.AccountTokenRepository
	Announcement() repository.AnnouncementRepository
	Vendor() repository.VendorRepository
	Partner() repository.PartnerRepository
	Client() repository.ClientRepository
	ClientClone() repository.ClientRepository
	ClientCloneBL() repository.ClientRepository
	City() repository.CityRepository
	Country() repository.CountryRepository
	District() repository.DistrictRepository
	Embargo() repository.EmbargoRepository
	ProductType() repository.ProductTypeRepository
	Commodity() repository.CommodityRepository
	Route() repository.RouteRepository
	RouteLeg() repository.RouteLegRepository
	Leg() repository.LegRepository
	Transport() repository.TransportRepository
	LimitRule() repository.LimitRuleRepository
	CityRate() repository.CityRateRepository
	CityRateDetail() repository.CityRateDetailRepository
	RateVersion() repository.RateVersionRepository
	RateVersionClone() repository.RateVersionRepository
	RateVersionCloneBL() repository.RateVersionRepository
	DistrictRate() repository.DistrictRateRepository
	DistrictRateDetail() repository.DistrictRateDetailRepository
	ConfigurablePrice() repository.ConfigurablePriceRepository
	CommoditySurcharge() repository.CommoditySurchargeRepository
	RateActivityLog() repository.RateActivityLogRepository
	ClientCityRate() repository.ClientCityRateRepository
	ClientCityRateDetail() repository.ClientCityRateDetailRepository
	ClientCityRateClone() repository.ClientCityRateRepository
	ClientCityRateCloneBL() repository.ClientCityRateRepository
	ClientCityRateDetailClone() repository.ClientCityRateDetailRepository
	ClientCityRateDetailCloneBL() repository.ClientCityRateDetailRepository
	ClientDistrictRate() repository.ClientDistrictRateRepository
	ClientDistrictRateDetail() repository.ClientDistrictRateDetailRepository
	CityClient() repository.CityClientRepository
	DistrictClient() repository.DistrictClientRepository
	ExchangeRate() repository.ExchangeRateRepository
	GoodsTax() repository.GoodsTaxRepository
	Hub() repository.HubRepository
	Customer() repository.CustomerRepository
	EstimateSla() repository.EstimateSlaRepository
	PartnerLocation() repository.PartnerLocationRepository
	Driver() repository.DriverRepository
	Vehicle() repository.VehicleRepository
	BulkStt() repository.BulkSttRepository
	Stt() repository.SttRepository
	ConfigurableRule() repository.ConfigurableRuleRepository
	CargoProductType() repository.CargoProductTypeRepository
	BankAccount() repository.BankAccountRepository
	Airport() repository.AirportRepository
	BookingCommission() repository.BookingCommissionRepository
	DeliveryVendor() repository.DeliveryVendorRepository
	Dtpol() repository.DtpolRepository
	BookingCommissionTier() repository.BookingCommissionTierRepository
	Message() repository.MessageRepository
	MessageYmWAPremium() repository.MessageYmWaPremiumRepository
	Bank() repository.BankRepository
	ElexysIntegration() repository.ElexysIntegrationRepository
	Health() repository.HealthRepository
	Kejarcuan() repository.KejarcuanRepository
	SttPas() repository.SttPasRepository
	Cache() repository.CacheRepository
	PartnerLog() repository.PartnerLogRepository
	PredefinedHoliday() repository.PredefinedHolidayRepository
	ResolutionCenter() repository.ResolutionCenterRepository
	BulkDownloadRepository() repository.BulkDownloadRepository
	ConfigMiniapp() repository.ConfigMiniappRepository
	DistrictVendor() repository.DistrictVendorRepository
	Time() repository.TimeRepository
	Wallet() repository.WalletRepository
	BaggingGroupLocation() repository.BaggingGroupLocationRepository
	CargoConfiguration() repository.CargoConfigurationRepository
	PromoDiscountConfigurationRepository() repository.PromoDiscountConfigurationRepository

	Pubsub() repository.PubsubRepository
	ConfigurableProgressiveCommissionRepository() repository.ConfigurableProgressiveCommissionRepository
	ConfigurableProgressiveCommissionDetailRepository() repository.ConfigurableProgressiveCommissionDetailRepository
	BulkBookingTemplateRepo() repository.BulkBookingTemplateRepository
	ConfigurationBannerRepo() repository.ConfigurationBannerRepository

	AlgoRepo() repository.AlgoRepository
	OdooContactRepo() repository.OdooContactRepository

	ReadyToCargo() repository.ReadyToCargoRepository
	CutOffTimeRepository() repository.CutOffTimeRepository
	Event() repository.EventRepository
	CargoReverse() repository.CargoReverseRepository

	Region() repository.RegionRepository
	RegionCity() repository.RegionCityRepository

	CaseCategory() repository.CaseCategoryRepository
	ServiceDelay() repository.ServiceDelayRepository
	CourierManagement() repository.CourierManagementRepository
	AccountNotification() repository.AccountNotificationRepository
	RtcCityGroup() repository.RtcCityGroupRepository
	CodConfiguration() repository.CodConfigurationRepository
	BalanceMinusPenalty() repository.BalanceMinusPenaltyRepository
	FlagManagementRepository() repository.FlagManagementRepository
	PegasusRepository() repository.PegasusRepository
	ReasonRepo() repository.ReasonRepository
	CustomProcessRepo() repository.CustomProcessRepository
	DexBucketTiketConfiguration() repository.DexBucketTiketConfigurationRepository
	DexBucketTicketAccount() repository.DexBucketTicketAccountRepository
	DexFakeDexConfiguration() repository.DexFakeDexConfigurationRepository
	CsAccountLog() repository.CsAccountLogRepository
	PartnerSaldoDepositHistory() repository.PartnerSaldoDepositHistoryRepository
	MetabaseRepository() repository.MetabaseRepository
	TransactionRepository() repository.TransactionRepository
	PickUpManifestRepository() repository.PickUpManifestRepository
	ConfigurableWoodpackingCity() repository.ConfigurableWoodpackingCityRepository
	ConfigurableInsuranceTierDetail() repository.ConfigurableInsuranceTierDetailRepository
	PickupSchedule() repository.PickupScheduleRepository
	InternationalDocumentConfiguration() repository.InternationalDocumentConfigurationRepository
	RepLag() repository.ReplagRepository
	ConfigurableUserConsent() repository.UserConsentRepository
	PickupCorporateAddressRepository() repository.PickupCorporateAddressRepository
	PickupCommissionConfigurationRepository() repository.PickupCommissionConfigurationRepository
	BulkSttDetail() repository.BulkSttDetailRepository
	Firebase(firebaseFcm firebase.FirebaseFcm) repository.FirebaseRepository
	CargoConfigurationSearchFlight() repository.CargoConfigurationSearchFlightRepository
	ConfigDfodPastiInactivePeriodRepository() repository.ConfigDfodPastiInactivePeriodRepository
	ConfigDfodPastiExceptionRepository() repository.ConfigDfodPastiExceptionRepository
	LPTokenRepository() repository.LPTokenRepository
	LimitterRepository() repository.LimitterRepository
	CommodityCategory() repository.CommodityCategoryRepository
	OtpRepository() repository.OtpRepository
}

type repositoryRegistry struct {
	cfg config.Config
}

// NewRepoRegistry ...
func NewRepoRegistry(cfg config.Config) RepositoryRegistry {
	var r repositoryRegistry
	var once sync.Once

	once.Do(func() {
		r = repositoryRegistry{cfg: cfg}
	})

	return r
}

func (r repositoryRegistry) LPTokenRepository() repository.LPTokenRepository {
	var once sync.Once
	var lpTokenRepo repository.LPTokenRepository

	once.Do(func() {
		lpTokenRepo = repository.NewLPTokenRepository()
	})

	return lpTokenRepo
}

func (r repositoryRegistry) Account() repository.AccountRepository {
	var once sync.Once
	var userRepo repository.AccountRepository

	once.Do(func() {
		userRepo = repository.NewAccountRepository(&r.cfg)
	})

	return userRepo
}

func (r repositoryRegistry) ActivityLog() repository.ActivityLogRepository {
	var once sync.Once
	var activityLogRepo repository.ActivityLogRepository

	once.Do(func() {
		activityLogRepo = repository.NewActivityLogRepository(&r.cfg)
	})

	return activityLogRepo
}

func (r repositoryRegistry) DistrictVendor() repository.DistrictVendorRepository {
	var once sync.Once
	var districtVendorRepo repository.DistrictVendorRepository

	once.Do(func() {
		districtVendorRepo = repository.NewDistrictVendorRepository(&r.cfg, r.PartnerLog())
	})

	return districtVendorRepo
}

func (r repositoryRegistry) Role() repository.RoleRepository {
	var once sync.Once
	var roleRepo repository.RoleRepository

	once.Do(func() {
		roleRepo = repository.NewRoleRepository(&r.cfg)
	})

	return roleRepo
}

func (r repositoryRegistry) Bulk() repository.BulkRepository {
	var once sync.Once
	var bulkRepo repository.BulkRepository

	once.Do(func() {
		bulkRepo = repository.NewBulkRepository(&r.cfg)
	})

	return bulkRepo
}
func (r repositoryRegistry) AccountToken() repository.AccountTokenRepository {
	var once sync.Once
	var accountTokenRepo repository.AccountTokenRepository

	once.Do(func() {
		accountTokenRepo = repository.NewAccountTokenRepository(&r.cfg)
	})

	return accountTokenRepo
}

func (r repositoryRegistry) Announcement() repository.AnnouncementRepository {
	var once sync.Once
	var announcementRepo repository.AnnouncementRepository

	once.Do(func() {
		announcementRepo = repository.NewAnnouncementRepository(&r.cfg, r.PartnerLog())
	})

	return announcementRepo
}

func (r repositoryRegistry) Vendor() repository.VendorRepository {
	var once sync.Once
	var vendorRepo repository.VendorRepository

	once.Do(func() {
		vendorRepo = repository.NewVendorRepository(&r.cfg)
	})

	return vendorRepo
}

func (r repositoryRegistry) Partner() repository.PartnerRepository {
	var once sync.Once
	var partnerRepo repository.PartnerRepository

	once.Do(func() {
		partnerRepo = repository.NewPartnerRepository(&r.cfg, r.PartnerLog())
	})

	return partnerRepo
}

func (r repositoryRegistry) PartnerSaldoDepositHistory() repository.PartnerSaldoDepositHistoryRepository {
	var once sync.Once
	var partnerSaldoDepositHistoryRepo repository.PartnerSaldoDepositHistoryRepository

	once.Do(func() {
		partnerSaldoDepositHistoryRepo = repository.NewPartnerSaldoDepositHistoryRepository(&r.cfg, r.PartnerLog())
	})

	return partnerSaldoDepositHistoryRepo
}

func (r repositoryRegistry) Client() repository.ClientRepository {
	var once sync.Once
	var clientRepo repository.ClientRepository

	once.Do(func() {
		clientRepo = repository.NewClientRepository(&r.cfg)
	})

	return clientRepo
}

func (r repositoryRegistry) ClientClone() repository.ClientRepository {
	var once sync.Once
	var clientRepo repository.ClientRepository

	once.Do(func() {
		clientRepo = repository.NewClientCloneRepository(&r.cfg)
	})

	return clientRepo
}

func (r repositoryRegistry) ClientCloneBL() repository.ClientRepository {
	var once sync.Once
	var clientRepo repository.ClientRepository

	once.Do(func() {
		clientRepo = repository.NewClientCloneBukalapakRepository(&r.cfg)
	})

	return clientRepo
}

func (r repositoryRegistry) City() repository.CityRepository {
	var once sync.Once
	var cityRepo repository.CityRepository

	once.Do(func() {
		cityRepo = repository.NewCityRepository(&r.cfg, r.PartnerLog())
	})

	return cityRepo
}

func (r repositoryRegistry) Country() repository.CountryRepository {
	var once sync.Once
	var countryRepo repository.CountryRepository

	once.Do(func() {
		countryRepo = repository.NewCountryRepository(&r.cfg)
	})

	return countryRepo
}

func (r repositoryRegistry) District() repository.DistrictRepository {
	var once sync.Once
	var districtRepo repository.DistrictRepository

	once.Do(func() {
		districtRepo = repository.NewDistrictRepository(&r.cfg, r.PartnerLog())
	})

	return districtRepo
}

func (r repositoryRegistry) Embargo() repository.EmbargoRepository {
	var once sync.Once
	var embargoRepo repository.EmbargoRepository

	once.Do(func() {
		embargoRepo = repository.NewEmbargoRepository(&r.cfg)
	})

	return embargoRepo
}

func (r repositoryRegistry) ProductType() repository.ProductTypeRepository {
	var once sync.Once
	var productTypeRepo repository.ProductTypeRepository
	once.Do(func() {
		productTypeRepo = repository.NewProductTypeRepository(&r.cfg)
	})

	return productTypeRepo
}

func (r repositoryRegistry) Commodity() repository.CommodityRepository {
	var once sync.Once
	var commodityRepo repository.CommodityRepository
	once.Do(func() {
		commodityRepo = repository.NewCommodityRepository(&r.cfg, r.PartnerLog())
	})

	return commodityRepo
}

func (r repositoryRegistry) Route() repository.RouteRepository {
	var once sync.Once
	var routeRepo repository.RouteRepository
	once.Do(func() {
		routeRepo = repository.NewRouteRepository(&r.cfg)
	})

	return routeRepo
}

func (r repositoryRegistry) RouteLeg() repository.RouteLegRepository {
	var once sync.Once
	var routeLegRepo repository.RouteLegRepository
	once.Do(func() {
		routeLegRepo = repository.NewRouteLegRepository(&r.cfg)
	})

	return routeLegRepo
}

func (r repositoryRegistry) Leg() repository.LegRepository {
	var once sync.Once
	var legRepo repository.LegRepository
	once.Do(func() {
		legRepo = repository.NewLegRepository(&r.cfg)
	})

	return legRepo
}

func (r repositoryRegistry) Transport() repository.TransportRepository {
	var once sync.Once
	var transportRepo repository.TransportRepository
	once.Do(func() {
		transportRepo = repository.NewTransportRepository(&r.cfg)
	})

	return transportRepo
}

func (r repositoryRegistry) LimitRule() repository.LimitRuleRepository {
	var once sync.Once
	var limitRuleRepo repository.LimitRuleRepository
	once.Do(func() {
		limitRuleRepo = repository.NewLimitRuleRepository(&r.cfg)
	})

	return limitRuleRepo
}

func (r repositoryRegistry) CityRate() repository.CityRateRepository {
	var once sync.Once
	var cityRateRepo repository.CityRateRepository
	once.Do(func() {
		cityRateRepo = repository.NewCityRateRepository(&r.cfg)
	})

	return cityRateRepo
}

func (r repositoryRegistry) CityRateDetail() repository.CityRateDetailRepository {
	var once sync.Once
	var cityRateDetailRepo repository.CityRateDetailRepository
	once.Do(func() {
		cityRateDetailRepo = repository.NewCityRateDetailRepository(&r.cfg)
	})

	return cityRateDetailRepo
}

func (r repositoryRegistry) DistrictRate() repository.DistrictRateRepository {
	var once sync.Once
	var districtRateRepo repository.DistrictRateRepository
	once.Do(func() {
		districtRateRepo = repository.NewDistrictRateRepository(&r.cfg)
	})

	return districtRateRepo
}

func (r repositoryRegistry) DistrictRateDetail() repository.DistrictRateDetailRepository {
	var once sync.Once
	var districtRateDetailRepo repository.DistrictRateDetailRepository
	once.Do(func() {
		districtRateDetailRepo = repository.NewDistrictRateDetailRepository(&r.cfg)
	})

	return districtRateDetailRepo
}

func (r repositoryRegistry) RateVersion() repository.RateVersionRepository {
	var once sync.Once
	var rateVersionRepo repository.RateVersionRepository
	once.Do(func() {
		rateVersionRepo = repository.NewRateVersionRepository(&r.cfg)
	})

	return rateVersionRepo
}

func (r repositoryRegistry) RateVersionClone() repository.RateVersionRepository {
	var once sync.Once
	var rateVersionRepo repository.RateVersionRepository
	once.Do(func() {
		rateVersionRepo = repository.NewRateVersionCloneRepository(&r.cfg)
	})

	return rateVersionRepo
}

func (r repositoryRegistry) RateVersionCloneBL() repository.RateVersionRepository {
	var once sync.Once
	var rateVersionRepo repository.RateVersionRepository
	once.Do(func() {
		rateVersionRepo = repository.NewRateVersionCloneBukalapakRepository(&r.cfg)
	})

	return rateVersionRepo
}

func (r repositoryRegistry) ConfigurablePrice() repository.ConfigurablePriceRepository {
	var once sync.Once
	var configurablePriceRepo repository.ConfigurablePriceRepository
	once.Do(func() {
		configurablePriceRepo = repository.NewConfigurablePrice(&r.cfg)
	})

	return configurablePriceRepo
}

func (r repositoryRegistry) CommoditySurcharge() repository.CommoditySurchargeRepository {
	var once sync.Once
	var commoditySurchargeRepo repository.CommoditySurchargeRepository
	once.Do(func() {
		commoditySurchargeRepo = repository.NewCommoditySurchargeRepository(&r.cfg)
	})

	return commoditySurchargeRepo
}

func (r repositoryRegistry) RateActivityLog() repository.RateActivityLogRepository {
	var once sync.Once
	var rateActivityLogRepo repository.RateActivityLogRepository
	once.Do(func() {
		rateActivityLogRepo = repository.NewRateActivityLogRepository(&r.cfg)
	})

	return rateActivityLogRepo
}

func (r repositoryRegistry) ClientCityRate() repository.ClientCityRateRepository {
	var once sync.Once
	var clientCityRateRepo repository.ClientCityRateRepository
	once.Do(func() {
		clientCityRateRepo = repository.NewClientCityRateRepository(&r.cfg)
	})

	return clientCityRateRepo
}

func (r repositoryRegistry) ClientCityRateClone() repository.ClientCityRateRepository {
	var once sync.Once
	var clientCityRateRepo repository.ClientCityRateRepository
	once.Do(func() {
		clientCityRateRepo = repository.NewClientCityRateCloneRepository(&r.cfg)
	})

	return clientCityRateRepo
}

func (r repositoryRegistry) ClientCityRateCloneBL() repository.ClientCityRateRepository {
	var once sync.Once
	var clientCityRateRepo repository.ClientCityRateRepository
	once.Do(func() {
		clientCityRateRepo = repository.NewClientCityRateCloneBukalapakRepository(&r.cfg)
	})

	return clientCityRateRepo
}

func (r repositoryRegistry) ClientCityRateDetail() repository.ClientCityRateDetailRepository {
	var once sync.Once
	var clientCityRateDetailRepo repository.ClientCityRateDetailRepository
	once.Do(func() {
		clientCityRateDetailRepo = repository.NewClientCityRateDetailRepository(&r.cfg)
	})

	return clientCityRateDetailRepo
}

func (r repositoryRegistry) ClientCityRateDetailClone() repository.ClientCityRateDetailRepository {
	var once sync.Once
	var clientCityRateDetailRepo repository.ClientCityRateDetailRepository
	once.Do(func() {
		clientCityRateDetailRepo = repository.NewClientCityRateDetailCloneRepository(&r.cfg)
	})

	return clientCityRateDetailRepo
}

func (r repositoryRegistry) ClientCityRateDetailCloneBL() repository.ClientCityRateDetailRepository {
	var once sync.Once
	var clientCityRateDetailRepo repository.ClientCityRateDetailRepository
	once.Do(func() {
		clientCityRateDetailRepo = repository.NewClientCityRateDetailCloneBukalapakRepository(&r.cfg)
	})

	return clientCityRateDetailRepo
}

func (r repositoryRegistry) ClientDistrictRate() repository.ClientDistrictRateRepository {
	var once sync.Once
	var clientDistrictRateRepo repository.ClientDistrictRateRepository
	once.Do(func() {
		clientDistrictRateRepo = repository.NewClientDistrictRateRepository(&r.cfg)
	})

	return clientDistrictRateRepo
}

func (r repositoryRegistry) ClientDistrictRateDetail() repository.ClientDistrictRateDetailRepository {
	var once sync.Once
	var clientDistrictRateDetailRepo repository.ClientDistrictRateDetailRepository
	once.Do(func() {
		clientDistrictRateDetailRepo = repository.NewClientDistrictRateDetailRepository(&r.cfg)
	})

	return clientDistrictRateDetailRepo
}
func (r repositoryRegistry) CityClient() repository.CityClientRepository {
	var once sync.Once
	var cityClientRepo repository.CityClientRepository
	once.Do(func() {
		cityClientRepo = repository.NewCityClientRepository(&r.cfg)
	})

	return cityClientRepo
}

func (r repositoryRegistry) DistrictClient() repository.DistrictClientRepository {
	var once sync.Once
	var districtClientRepo repository.DistrictClientRepository
	once.Do(func() {
		districtClientRepo = repository.NewDistrictClientRepository(&r.cfg)
	})

	return districtClientRepo
}

func (r repositoryRegistry) ExchangeRate() repository.ExchangeRateRepository {
	var once sync.Once
	var exchangeRateRepo repository.ExchangeRateRepository
	once.Do(func() {
		exchangeRateRepo = repository.NewExchangeRateRepository(&r.cfg)
	})

	return exchangeRateRepo
}

func (r repositoryRegistry) GoodsTax() repository.GoodsTaxRepository {
	var once sync.Once
	var goodsTaxRepo repository.GoodsTaxRepository
	once.Do(func() {
		goodsTaxRepo = repository.NewGoodsTaxRepository(&r.cfg)
	})

	return goodsTaxRepo
}

func (r repositoryRegistry) Hub() repository.HubRepository {
	var once sync.Once
	var hubRepo repository.HubRepository
	once.Do(func() {
		hubRepo = repository.NewHubRepository(&r.cfg)
	})

	return hubRepo
}

func (r repositoryRegistry) Customer() repository.CustomerRepository {
	var once sync.Once
	var customerRepo repository.CustomerRepository

	once.Do(func() {
		if r.cfg.OpenSearchEnable() {
			customerRepo = repository.NewCustomerOpenSearchRepository(&r.cfg, r.PartnerLog())
			return
		}
		customerRepo = repository.NewCustomerRepository(&r.cfg, r.PartnerLog())
	})

	return customerRepo
}

func (r repositoryRegistry) EstimateSla() repository.EstimateSlaRepository {
	var once sync.Once
	var estimateSlaRepo repository.EstimateSlaRepository

	once.Do(func() {
		estimateSlaRepo = repository.NewEstimateSlaRepository(&r.cfg)
	})

	return estimateSlaRepo
}

func (r repositoryRegistry) PartnerLocation() repository.PartnerLocationRepository {
	var once sync.Once
	var partnerLocationRepo repository.PartnerLocationRepository

	once.Do(func() {
		partnerLocationRepo = repository.NewPartnerLocationRepository(&r.cfg)
	})

	return partnerLocationRepo
}

func (r repositoryRegistry) Vehicle() repository.VehicleRepository {
	var once sync.Once
	var vehicleRepo repository.VehicleRepository

	once.Do(func() {
		vehicleRepo = repository.NewVehicleRepository(&r.cfg)
	})

	return vehicleRepo
}

func (r repositoryRegistry) Driver() repository.DriverRepository {
	var once sync.Once
	var driverRepo repository.DriverRepository

	once.Do(func() {
		driverRepo = repository.NewDriverRepository(&r.cfg)
	})

	return driverRepo
}

func (r repositoryRegistry) BulkStt() repository.BulkSttRepository {
	var once sync.Once
	var bulkSttRepo repository.BulkSttRepository

	once.Do(func() {
		bulkSttRepo = repository.NewBulkSttRepository(&r.cfg)
	})

	return bulkSttRepo
}

func (r repositoryRegistry) Stt() repository.SttRepository {
	var once sync.Once
	var userRepo repository.SttRepository

	once.Do(func() {
		userRepo = repository.NewSttRepository(&r.cfg)
	})

	return userRepo
}

func (r repositoryRegistry) ConfigurableRule() repository.ConfigurableRuleRepository {
	var once sync.Once
	var configurableRuleRepo repository.ConfigurableRuleRepository

	once.Do(func() {
		configurableRuleRepo = repository.NewConfigurableRepository(&r.cfg)
	})

	return configurableRuleRepo
}

func (r repositoryRegistry) CargoProductType() repository.CargoProductTypeRepository {
	var once sync.Once
	var cargoProductTypeRepo repository.CargoProductTypeRepository

	once.Do(func() {
		cargoProductTypeRepo = repository.NewCargoProductTypeRepository(&r.cfg)
	})

	return cargoProductTypeRepo
}

func (r repositoryRegistry) BankAccount() repository.BankAccountRepository {
	var once sync.Once
	var BankAccountRepo repository.BankAccountRepository

	once.Do(func() {
		BankAccountRepo = repository.NewBankAccountRepository(&r.cfg)
	})

	return BankAccountRepo
}

func (r repositoryRegistry) Airport() repository.AirportRepository {
	var once sync.Once
	var airportRepo repository.AirportRepository

	once.Do(func() {
		airportRepo = repository.NewAirportRepository(&r.cfg)
	})

	return airportRepo
}

func (r repositoryRegistry) BookingCommission() repository.BookingCommissionRepository {
	var once sync.Once
	var bookingCommissionRepo repository.BookingCommissionRepository

	once.Do(func() {
		bookingCommissionRepo = repository.NewBookingCommissionRepository(&r.cfg, r.PartnerLog())
	})

	return bookingCommissionRepo
}

func (r repositoryRegistry) DeliveryVendor() repository.DeliveryVendorRepository {
	var once sync.Once
	var deliveryVendorRepo repository.DeliveryVendorRepository

	once.Do(func() {
		deliveryVendorRepo = repository.NewDeliveryVendorRepository(&r.cfg)
	})

	return deliveryVendorRepo
}

func (r repositoryRegistry) Dtpol() repository.DtpolRepository {
	var once sync.Once
	var dtpolRepo repository.DtpolRepository

	once.Do(func() {
		dtpolRepo = repository.NewDtpolRepository(&r.cfg)
	})

	return dtpolRepo
}

func (r repositoryRegistry) BookingCommissionTier() repository.BookingCommissionTierRepository {
	var once sync.Once
	var bookingCommissionTierRepo repository.BookingCommissionTierRepository

	once.Do(func() {
		bookingCommissionTierRepo = repository.NewBookingCommissionTierRepository(&r.cfg)
	})

	return bookingCommissionTierRepo
}

func (r repositoryRegistry) Message() repository.MessageRepository {
	var once sync.Once
	var MessageRepo repository.MessageRepository

	once.Do(func() {
		MessageRepo = repository.NewMessageRepository(&r.cfg)
	})

	return MessageRepo
}

func (r repositoryRegistry) MessageYmWAPremium() repository.MessageYmWaPremiumRepository {
	var once sync.Once
	var MessageYmWaRepo repository.MessageYmWaPremiumRepository

	once.Do(func() {
		MessageYmWaRepo = repository.NewMessageYmWaPremiumRepository(&r.cfg)
	})

	return MessageYmWaRepo
}

func (r repositoryRegistry) Bank() repository.BankRepository {
	var once sync.Once
	var BankRepo repository.BankRepository

	once.Do(func() {
		BankRepo = repository.NewBankRepository(&r.cfg)
	})

	return BankRepo
}

func (r repositoryRegistry) ElexysIntegration() repository.ElexysIntegrationRepository {
	var once sync.Once
	var elexysIntegrationRepo repository.ElexysIntegrationRepository

	once.Do(func() {
		elexysIntegrationRepo = repository.NewElexysIntegrationRepository(&r.cfg)
	})

	return elexysIntegrationRepo
}

func (r repositoryRegistry) Health() repository.HealthRepository {
	var once sync.Once
	var healthRepository repository.HealthRepository

	once.Do(func() {
		healthRepository = repository.NewHealthRepository(&r.cfg)
	})

	return healthRepository
}

func (r repositoryRegistry) Kejarcuan() repository.KejarcuanRepository {
	var once sync.Once
	var kejarcuanRepository repository.KejarcuanRepository

	once.Do(func() {
		kejarcuanRepository = repository.NewKejarcuanRepository(&r.cfg)
	})

	return kejarcuanRepository
}

func (r repositoryRegistry) SttPas() repository.SttPasRepository {
	var once sync.Once
	var sttPasRepository repository.SttPasRepository

	once.Do(func() {
		sttPasRepository = repository.NewSttPasRepository(&r.cfg)
	})

	return sttPasRepository
}

func (r repositoryRegistry) Cache() repository.CacheRepository {
	var once sync.Once
	var cacheRepository repository.CacheRepository

	once.Do(func() {
		cacheRepository = repository.NewCacheRepository(&r.cfg)
	})

	return cacheRepository
}

func (r repositoryRegistry) PartnerLog() repository.PartnerLogRepository {
	var once sync.Once
	var plRepository repository.PartnerLogRepository

	once.Do(func() {
		plRepository = repository.NewPartnerLogRepository(&r.cfg)
	})

	return plRepository
}

func (r repositoryRegistry) PredefinedHoliday() repository.PredefinedHolidayRepository {
	var once sync.Once
	var phRepository repository.PredefinedHolidayRepository

	once.Do(func() {
		phRepository = repository.NewPredefinedHolidayRepository(&r.cfg)
	})

	return phRepository
}

func (r repositoryRegistry) ResolutionCenter() repository.ResolutionCenterRepository {
	var once sync.Once
	var rsRepository repository.ResolutionCenterRepository

	once.Do(func() {
		rsRepository = repository.NewResolutionCenterRepository(&r.cfg)
	})

	return rsRepository
}

func (r repositoryRegistry) BulkDownloadRepository() repository.BulkDownloadRepository {
	var once sync.Once
	var re repository.BulkDownloadRepository

	once.Do(func() {
		re = repository.NewBulkDownloadRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) ConfigMiniapp() repository.ConfigMiniappRepository {
	var once sync.Once
	var re repository.ConfigMiniappRepository

	once.Do(func() {
		re = repository.NewConfigMiniappRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) Time() repository.TimeRepository {
	var once sync.Once
	var timeRepository repository.TimeRepository

	once.Do(func() {
		timeRepository = repository.NewTimeRepository()
	})

	return timeRepository
}

func (r repositoryRegistry) Pubsub() repository.PubsubRepository {
	var once sync.Once
	var re repository.PubsubRepository

	once.Do(func() {
		re = repository.NewPubsubRepository(
			&r.cfg,
		)
	})

	return re
}

func (r repositoryRegistry) ConfigurableProgressiveCommissionRepository() repository.ConfigurableProgressiveCommissionRepository {
	var once sync.Once
	var re repository.ConfigurableProgressiveCommissionRepository

	once.Do(func() {
		re = repository.NewConfigurableProgressiveCommissionRepository(
			&r.cfg,
		)
	})

	return re
}

func (r repositoryRegistry) ConfigurableProgressiveCommissionDetailRepository() repository.ConfigurableProgressiveCommissionDetailRepository {
	var once sync.Once
	var re repository.ConfigurableProgressiveCommissionDetailRepository

	once.Do(func() {
		re = repository.NewConfigurableProgressiveCommissionDetailRepository(
			&r.cfg,
		)
	})

	return re
}

func (r repositoryRegistry) Wallet() repository.WalletRepository {
	var once sync.Once
	var res repository.WalletRepository

	once.Do(func() {
		res = repository.NewWalletRepository(
			&r.cfg,
		)
	})

	return res
}

func (r repositoryRegistry) BulkBookingTemplateRepo() repository.BulkBookingTemplateRepository {
	var once sync.Once
	var res repository.BulkBookingTemplateRepository

	once.Do(func() {
		res = repository.NewBulkBookingTemplateRepository(
			&r.cfg,
		)
	})

	return res
}

func (r repositoryRegistry) BaggingGroupLocation() repository.BaggingGroupLocationRepository {
	var once sync.Once
	var baggingGroupLocationRepository repository.BaggingGroupLocationRepository

	once.Do(func() {
		baggingGroupLocationRepository = repository.NewBaggingGroupLocationRepository(
			&r.cfg,
		)
	})

	return baggingGroupLocationRepository
}

func (r repositoryRegistry) CargoConfiguration() repository.CargoConfigurationRepository {
	var once sync.Once
	var cargoConfigurationRepo repository.CargoConfigurationRepository

	once.Do(func() {
		cargoConfigurationRepo = repository.NewCargoConfigurationRepository(
			&r.cfg,
		)
	})

	return cargoConfigurationRepo
}

func (r repositoryRegistry) ConfigurationBannerRepo() repository.ConfigurationBannerRepository {
	var once sync.Once
	var configurationBannerRepo repository.ConfigurationBannerRepository

	once.Do(func() {
		configurationBannerRepo = repository.NewConfigurationBannerRepository(
			&r.cfg,
		)
	})

	return configurationBannerRepo
}

func (r repositoryRegistry) AlgoRepo() repository.AlgoRepository {
	var once sync.Once
	var algoRepo repository.AlgoRepository

	once.Do(func() {
		algoRepo = repository.NewAlgoRepository(
			&r.cfg,
		)
	})

	return algoRepo
}

func (r repositoryRegistry) OdooContactRepo() repository.OdooContactRepository {
	var once sync.Once
	var odooIntegrationRepo repository.OdooContactRepository

	once.Do(func() {
		odooIntegrationRepo = repository.NewOdooContactRepository(
			&r.cfg,
		)
	})

	return odooIntegrationRepo
}

func (r repositoryRegistry) ReadyToCargo() repository.ReadyToCargoRepository {
	var once sync.Once
	var readyToCargo repository.ReadyToCargoRepository

	once.Do(func() {
		readyToCargo = repository.NewReadyToCargoRepository(
			&r.cfg,
		)
	})

	return readyToCargo
}

func (r repositoryRegistry) PromoDiscountConfigurationRepository() repository.PromoDiscountConfigurationRepository {
	var once sync.Once
	var promoDiscountConfigurationRepository repository.PromoDiscountConfigurationRepository
	once.Do(func() {
		promoDiscountConfigurationRepository = repository.NewPromoDiscountConfigurationRepository(&r.cfg)
	})

	return promoDiscountConfigurationRepository
}

func (r repositoryRegistry) CutOffTimeRepository() repository.CutOffTimeRepository {
	var once sync.Once
	var cutOffTimeRepository repository.CutOffTimeRepository
	once.Do(func() {
		cutOffTimeRepository = repository.NewCutOffTimeRepository(&r.cfg)
	})

	return cutOffTimeRepository
}

func (r repositoryRegistry) Event() repository.EventRepository {
	var once sync.Once
	var event repository.EventRepository

	once.Do(func() {
		event = repository.NewEventRepository(
			&r.cfg,
		)
	})

	return event
}

func (r repositoryRegistry) CargoReverse() repository.CargoReverseRepository {
	var once sync.Once
	var CargoReverse repository.CargoReverseRepository
	once.Do(func() {
		CargoReverse = repository.NewCargoReverseRepository(&r.cfg)
	})

	return CargoReverse
}

func (r repositoryRegistry) Region() repository.RegionRepository {
	var once sync.Once
	var Region repository.RegionRepository
	once.Do(func() {
		Region = repository.NewRegionRepository(&r.cfg)
	})

	return Region
}

func (r repositoryRegistry) RegionCity() repository.RegionCityRepository {
	var once sync.Once
	var RegionCity repository.RegionCityRepository
	once.Do(func() {
		RegionCity = repository.NewRegionCityRepository(&r.cfg)
	})

	return RegionCity
}

func (r repositoryRegistry) CaseCategory() repository.CaseCategoryRepository {
	var once sync.Once
	var caseCategory repository.CaseCategoryRepository
	once.Do(func() {
		caseCategory = repository.NewCaseCategoryRepository(&r.cfg)
	})

	return caseCategory
}

func (r repositoryRegistry) ServiceDelay() repository.ServiceDelayRepository {
	var once sync.Once
	var ServiceDelay repository.ServiceDelayRepository
	once.Do(func() {
		ServiceDelay = repository.NewServiceDelayRepository(&r.cfg, r.PartnerLog())
	})

	return ServiceDelay
}

func (r repositoryRegistry) ConfigurableCod() repository.ConfigurableCodRepository {
	var once sync.Once
	var ConfigurableCodRepo repository.ConfigurableCodRepository

	once.Do(func() {
		ConfigurableCodRepo = repository.NewConfigurableCodRepository(&r.cfg)
	})

	return ConfigurableCodRepo
}

func (r repositoryRegistry) CourierManagement() repository.CourierManagementRepository {
	var once sync.Once
	var courierManagement repository.CourierManagementRepository

	once.Do(func() {
		courierManagement = repository.NewCourierManagementRepository(&r.cfg)
	})

	return courierManagement
}

func (r repositoryRegistry) AccountNotification() repository.AccountNotificationRepository {
	var once sync.Once

	var accountNotification repository.AccountNotificationRepository

	once.Do(func() {
		accountNotification = repository.NewAccountNotificationRepository(&r.cfg)
	})

	return accountNotification
}

func (r repositoryRegistry) RtcCityGroup() repository.RtcCityGroupRepository {
	var once sync.Once
	var rtcCityGroup repository.RtcCityGroupRepository

	once.Do(func() {
		rtcCityGroup = repository.NewRtcCityGroupRepository(&r.cfg)
	})

	return rtcCityGroup
}

func (r repositoryRegistry) CodConfiguration() repository.CodConfigurationRepository {
	var once sync.Once

	var codConfiguration repository.CodConfigurationRepository

	once.Do(func() {
		codConfiguration = repository.NewCodConfigurationRepository(&r.cfg)
	})

	return codConfiguration
}

func (r repositoryRegistry) BalanceMinusPenalty() repository.BalanceMinusPenaltyRepository {
	var once sync.Once

	var balanceMinusPenalty repository.BalanceMinusPenaltyRepository

	once.Do(func() {
		balanceMinusPenalty = repository.NewBalanceMinusPenaltyRepository(&r.cfg)
	})

	return balanceMinusPenalty
}

func (r repositoryRegistry) FlagManagementRepository() repository.FlagManagementRepository {
	var once sync.Once
	var re repository.FlagManagementRepository

	once.Do(func() {
		re = repository.NewFlagManagementRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) PegasusRepository() repository.PegasusRepository {
	var once sync.Once
	var re repository.PegasusRepository

	once.Do(func() {
		re = repository.NewPegasusRepository(&r.cfg, r.PartnerLog())
	})

	return re
}

func (r repositoryRegistry) ReasonRepo() repository.ReasonRepository {
	var once sync.Once
	var re repository.ReasonRepository

	once.Do(func() {
		re = repository.NewReasonRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) CustomProcessRepo() repository.CustomProcessRepository {
	var once sync.Once
	var re repository.CustomProcessRepository

	once.Do(func() {
		re = repository.NewCustomProcessRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) DexBucketTiketConfiguration() repository.DexBucketTiketConfigurationRepository {
	var once sync.Once

	var dexBucketTiketConfiguration repository.DexBucketTiketConfigurationRepository

	once.Do(func() {
		dexBucketTiketConfiguration = repository.NewDexBucketTiketConfigurationRepository(&r.cfg)
	})

	return dexBucketTiketConfiguration
}

func (r repositoryRegistry) DexBucketTicketAccount() repository.DexBucketTicketAccountRepository {
	var once sync.Once

	var dexBucketTicketAccount repository.DexBucketTicketAccountRepository

	once.Do(func() {
		dexBucketTicketAccount = repository.NewDexBucketTicketAccountRepository(&r.cfg)
	})

	return dexBucketTicketAccount
}

func (r repositoryRegistry) DexFakeDexConfiguration() repository.DexFakeDexConfigurationRepository {
	var once sync.Once

	var dexFakeDexConfiguration repository.DexFakeDexConfigurationRepository

	once.Do(func() {
		dexFakeDexConfiguration = repository.NewDexFakeDexConfigurationRepository(&r.cfg)
	})

	return dexFakeDexConfiguration
}

func (r repositoryRegistry) CsAccountLog() repository.CsAccountLogRepository {
	var once sync.Once

	var csAccountLog repository.CsAccountLogRepository

	once.Do(func() {
		csAccountLog = repository.NewCsAccountLogRepository(&r.cfg)
	})

	return csAccountLog
}

func (r repositoryRegistry) MetabaseRepository() repository.MetabaseRepository {
	var once sync.Once

	var repo repository.MetabaseRepository

	once.Do(func() {
		repo = repository.NewMetabaseRepository(&r.cfg)
	})

	return repo
}

func (r repositoryRegistry) TransactionRepository() repository.TransactionRepository {
	var once sync.Once

	var repo repository.TransactionRepository

	once.Do(func() {
		repo = repository.NewTransactionRepository(&r.cfg)
	})

	return repo
}

func (r repositoryRegistry) PickUpManifestRepository() repository.PickUpManifestRepository {
	var once sync.Once

	var repo repository.PickUpManifestRepository

	once.Do(func() {
		repo = repository.NewPickUpManifestRepository(&r.cfg)
	})

	return repo
}

func (r repositoryRegistry) ConfigurableWoodpackingCity() repository.ConfigurableWoodpackingCityRepository {
	var once sync.Once

	var configurableWoodpackingCity repository.ConfigurableWoodpackingCityRepository

	once.Do(func() {
		configurableWoodpackingCity = repository.NewConfigurableWoodpackingCityRepository(&r.cfg)
	})

	return configurableWoodpackingCity
}

func (r repositoryRegistry) ConfigurableInsuranceTierDetail() repository.ConfigurableInsuranceTierDetailRepository {
	var once sync.Once

	var ConfigurableInsuranceTierDetail repository.ConfigurableInsuranceTierDetailRepository

	once.Do(func() {
		ConfigurableInsuranceTierDetail = repository.NewConfigurableInsuranceTierDetail(&r.cfg)
	})

	return ConfigurableInsuranceTierDetail
}

func (r repositoryRegistry) PickupSchedule() repository.PickupScheduleRepository {
	var once sync.Once

	var pickupScheduleRepo repository.PickupScheduleRepository

	once.Do(func() {
		pickupScheduleRepo = repository.NewPickupScheduleRepository(&r.cfg)
	})

	return pickupScheduleRepo
}

func (r repositoryRegistry) InternationalDocumentConfiguration() repository.InternationalDocumentConfigurationRepository {
	var once sync.Once

	var InternationalDocumentConfiguration repository.InternationalDocumentConfigurationRepository

	once.Do(func() {
		InternationalDocumentConfiguration = repository.NewInternationalDocumentConfigurationRepository(&r.cfg)
	})

	return InternationalDocumentConfiguration
}

func (r repositoryRegistry) RepLag() repository.ReplagRepository {
	var once sync.Once
	var repo repository.ReplagRepository
	once.Do(func() {
		repo = repository.NewReplagRepository(&r.cfg)
	})

	return repo
}

func (r repositoryRegistry) ConfigDfodPastiInactivePeriodRepository() repository.ConfigDfodPastiInactivePeriodRepository {
	var once sync.Once
	var repo repository.ConfigDfodPastiInactivePeriodRepository
	once.Do(func() {
		repo = repository.NewConfigDfodPastiInactivePeriodRepository(&r.cfg)
	})

	return repo
}

func (r repositoryRegistry) ConfigurableUserConsent() repository.UserConsentRepository {
	var once sync.Once

	var ConfigurableUserConsent repository.UserConsentRepository

	once.Do(func() {
		ConfigurableUserConsent = repository.NewUserConsentRepository(
			repository.SetUserConsentRepoCFG(&r.cfg),
			repository.SetUserConsentRepoCache(r.cfg.Cache()),
			repository.SetUserConsentRepoDB(r.cfg.DB()),
		)
	})

	return ConfigurableUserConsent
}

func (r repositoryRegistry) BulkSttDetail() repository.BulkSttDetailRepository {
	var once sync.Once
	var bulkSttDetailRepo repository.BulkSttDetailRepository

	once.Do(func() {
		bulkSttDetailRepo = repository.NewBulkSttDetailRepository(&r.cfg)
	})

	return bulkSttDetailRepo
}

func (r repositoryRegistry) Firebase(firebaseFcm firebase.FirebaseFcm) repository.FirebaseRepository {
	var once sync.Once
	var firebaseRepo repository.FirebaseRepository

	once.Do(func() {
		firebaseRepo = repository.NewFirebaseRepository(
			&r.cfg,
			firebaseFcm,
			r.PartnerLog(),
			r.AccountNotification(),
		)
	})

	return firebaseRepo
}

func (r repositoryRegistry) CargoConfigurationSearchFlight() repository.CargoConfigurationSearchFlightRepository {
	var once sync.Once
	var repo repository.CargoConfigurationSearchFlightRepository
	once.Do(func() {
		repo = repository.NewCargoConfigurationSearchFlightRepository(
			repository.SetCargoConfigurationSearchFlightRepoCFG(&r.cfg),
			repository.SetCargoConfigurationSearchFlightRepoCache(r.cfg.Cache()),
			repository.SetCargoConfigurationSearchFlightRepoDB(r.cfg.DB()),
		)
	})

	return repo
}

func (r repositoryRegistry) ConfigDfodPastiExceptionRepository() repository.ConfigDfodPastiExceptionRepository {
	var once sync.Once
	var repo repository.ConfigDfodPastiExceptionRepository
	once.Do(func() {
		repo = repository.NewConfigDfodPastiExceptionRepository(&r.cfg)
	})

	return repo
}

func (r repositoryRegistry) LimitterRepository() repository.LimitterRepository {
	var once sync.Once
	var limitterRepo repository.LimitterRepository

	once.Do(func() {
		limitterRepo = repository.NewLimitterRepository(&r.cfg)
	})

	return limitterRepo
}

func (r repositoryRegistry) CommodityCategory() repository.CommodityCategoryRepository {
	var once sync.Once
	var repo repository.CommodityCategoryRepository

	once.Do(func() {
		repo = repository.NewCommodityCategoryRepository(&r.cfg, r.PartnerLog())
	})

	return repo
}

func (r repositoryRegistry) OtpRepository() repository.OtpRepository {
	var once sync.Once
	var otpRepo repository.OtpRepository

	once.Do(func() {
		otpRepo = repository.NewOtpRepository(&r.cfg)
	})

	return otpRepo
}
