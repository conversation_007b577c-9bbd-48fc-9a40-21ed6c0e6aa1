package repository

import (
	"github.com/Lionparcel/horde/src/repository"
	"sync"
)

func (r repositoryRegistry) PickupCommissionConfigurationRepository() repository.PickupCommissionConfigurationRepository {
	var once sync.Once
	var pickupCommissionConfigurationRepo repository.PickupCommissionConfigurationRepository

	once.Do(func() {
		pickupCommissionConfigurationRepo = repository.NewPickupCommissionConfigurationRepo(&r.cfg)
	})

	return pickupCommissionConfigurationRepo
}
