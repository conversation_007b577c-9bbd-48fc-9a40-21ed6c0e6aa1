package registry

import (
	"sync"

	"github.com/Lionparcel/horde/config"
	configurable_cashback "github.com/Lionparcel/horde/src/modules/configurable_cashback/repository"
	confTopUpFeeRepo "github.com/Lionparcel/horde/src/modules/configurable_top_up_fee/repository"
	deviceauth "github.com/Lionparcel/horde/src/modules/deviceauth/repository"
	configurable_partner "github.com/Lionparcel/horde/src/modules/shared/configurable_partner/repository"
	tariff "github.com/Lionparcel/horde/src/modules/tariff/repository"
	"gorm.io/gorm"
)

// ModuleRepositoryRegistry ...
type ModuleRepositoryRegistry interface {
	ConfigurableTopUpFee() confTopUpFeeRepo.ConfigurableTopUpFeeRepository
	ConfigurablePartnerRepository() configurable_partner.ConfigurablePartnerRepository
	ConfigurableCashbackRepository() configurable_cashback.ConfigurableCashbackRepository

	// Tariff module
	TariffCityRateRepository() tariff.CityRateRepository
	TariffClientCityRateRepository() tariff.ClientCityRateRepository
	TariffClientDistrictRateRepository() tariff.ClientDistrictRateRepository
	TariffCodConfigurationRepository() tariff.CodConfigurationRepository
	TariffCommoditySurchargeRepository() tariff.CommoditySurchargeRepository
	TariffConfigurablePriceRepository() tariff.ConfigurablePriceRepository
	TariffConfigurableRuleRepository() tariff.ConfigurableRuleRepository
	TariffConfigurableWoodpackingCityRepository() tariff.ConfigurableWoodpackingCityRepository
	TariffDistrictRateRepository() tariff.DistrictRateRepository
	TariffEstimateSLARepository() tariff.EstimateSlaRepository
	TariffPromoDiscountConfigurationRepository() tariff.PromoDiscountConfigurationRepository
	TariffConfigurableInsuranceTierDetailRepository() tariff.ConfigurableInsuranceTierDetailRepository
	TariffRateVersionRepository() tariff.RateVersionRepository
	TariffEmbargoRepository() tariff.EmbargoRepository
	TariffRateVersionClone() tariff.RateVersionRepository
	TariffRateVersionCloneBL() tariff.RateVersionRepository
	TariffClientCityRateClone() tariff.ClientCityRateRepository
	TariffClientCityRateCloneBL() tariff.ClientCityRateRepository
	TariffClientDistrictRateCloneRepository() tariff.ClientDistrictRateRepository
	TariffClientDistrictRateCloneBlRepository() tariff.ClientDistrictRateRepository
	SttPasRepository() tariff.SttPasRepository
	DeviceRepository() deviceauth.DeviceRepository
	DeviceApprovalRepository() deviceauth.DeviceApprovalRepository
	DeviceAuthLogRepository() deviceauth.DeviceAuthLogRepository
}

type moduleRepositoryRegistry struct {
	cfg config.Config
	DB  *gorm.DB
}

// NewModuleRepoRegistry ...
func NewModuleRepoRegistry(cfg config.Config) ModuleRepositoryRegistry {
	var r moduleRepositoryRegistry
	var once sync.Once

	once.Do(func() {
		r = moduleRepositoryRegistry{cfg: cfg}
	})

	return r
}

func (r moduleRepositoryRegistry) ConfigurablePartnerRepository() configurable_partner.ConfigurablePartnerRepository {
	var once sync.Once
	var repo configurable_partner.ConfigurablePartnerRepository
	once.Do(func() {
		repo = configurable_partner.NewConfigurablePartnerRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) ConfigurableTopUpFee() confTopUpFeeRepo.ConfigurableTopUpFeeRepository {
	var once sync.Once
	var repo confTopUpFeeRepo.ConfigurableTopUpFeeRepository
	once.Do(func() {
		repo = confTopUpFeeRepo.NewConfigurableTopUpFeeRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) ConfigurableCashbackRepository() configurable_cashback.ConfigurableCashbackRepository {
	var once sync.Once
	var repo configurable_cashback.ConfigurableCashbackRepository

	once.Do(func() {
		repo = configurable_cashback.NewConfigurableCashbackRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffCityRateRepository() tariff.CityRateRepository {
	var once sync.Once
	var repo tariff.CityRateRepository

	once.Do(func() {
		repo = tariff.NewCityRateRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffClientCityRateRepository() tariff.ClientCityRateRepository {
	var once sync.Once
	var repo tariff.ClientCityRateRepository

	once.Do(func() {
		repo = tariff.NewClientCityRateRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffClientDistrictRateRepository() tariff.ClientDistrictRateRepository {
	var once sync.Once
	var repo tariff.ClientDistrictRateRepository

	once.Do(func() {
		repo = tariff.NewClientDistrictRateRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffCodConfigurationRepository() tariff.CodConfigurationRepository {
	var once sync.Once
	var repo tariff.CodConfigurationRepository

	once.Do(func() {
		repo = tariff.NewCodConfigurationRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffCommoditySurchargeRepository() tariff.CommoditySurchargeRepository {
	var once sync.Once
	var repo tariff.CommoditySurchargeRepository

	once.Do(func() {
		repo = tariff.NewCommoditySurchargeRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffConfigurablePriceRepository() tariff.ConfigurablePriceRepository {
	var once sync.Once
	var repo tariff.ConfigurablePriceRepository

	once.Do(func() {
		repo = tariff.NewConfigurablePrice(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffConfigurableRuleRepository() tariff.ConfigurableRuleRepository {
	var once sync.Once
	var repo tariff.ConfigurableRuleRepository

	once.Do(func() {
		repo = tariff.NewConfigurableRuleRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffConfigurableWoodpackingCityRepository() tariff.ConfigurableWoodpackingCityRepository {
	var once sync.Once
	var repo tariff.ConfigurableWoodpackingCityRepository

	once.Do(func() {
		repo = tariff.NewConfigurableWoodpackingCityRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffDistrictRateRepository() tariff.DistrictRateRepository {
	var once sync.Once
	var repo tariff.DistrictRateRepository

	once.Do(func() {
		repo = tariff.NewDistrictRateRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffEstimateSLARepository() tariff.EstimateSlaRepository {
	var once sync.Once
	var repo tariff.EstimateSlaRepository

	once.Do(func() {
		repo = tariff.NewEstimateSlaRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffPromoDiscountConfigurationRepository() tariff.PromoDiscountConfigurationRepository {
	var once sync.Once
	var repo tariff.PromoDiscountConfigurationRepository

	once.Do(func() {
		repo = tariff.NewPromoDiscountConfigurationRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffRateVersionRepository() tariff.RateVersionRepository {
	var once sync.Once
	var repo tariff.RateVersionRepository

	once.Do(func() {
		repo = tariff.NewRateVersionRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffConfigurableInsuranceTierDetailRepository() tariff.ConfigurableInsuranceTierDetailRepository {
	var once sync.Once
	var repo tariff.ConfigurableInsuranceTierDetailRepository

	once.Do(func() {
		repo = tariff.NewConfigurableInsuranceTierDetail(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffEmbargoRepository() tariff.EmbargoRepository {
	var once sync.Once
	var repo tariff.EmbargoRepository

	once.Do(func() {
		repo = tariff.NewEmbargoRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffRateVersionClone() tariff.RateVersionRepository {
	var once sync.Once
	var repo tariff.RateVersionRepository
	once.Do(func() {
		repo = tariff.NewRateVersionCloneRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffRateVersionCloneBL() tariff.RateVersionRepository {
	var once sync.Once
	var repo tariff.RateVersionRepository
	once.Do(func() {
		repo = tariff.NewRateVersionCloneBukalapakRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffClientCityRateClone() tariff.ClientCityRateRepository {
	var once sync.Once
	var repo tariff.ClientCityRateRepository
	once.Do(func() {
		repo = tariff.NewClientCityRateCloneRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffClientCityRateCloneBL() tariff.ClientCityRateRepository {
	var once sync.Once
	var repo tariff.ClientCityRateRepository
	once.Do(func() {
		repo = tariff.NewClientCityRateCloneBukalapakRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffClientDistrictRateCloneRepository() tariff.ClientDistrictRateRepository {
	var once sync.Once
	var repo tariff.ClientDistrictRateRepository

	once.Do(func() {
		repo = tariff.NewClientDistrictRateCloneRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) TariffClientDistrictRateCloneBlRepository() tariff.ClientDistrictRateRepository {
	var once sync.Once
	var repo tariff.ClientDistrictRateRepository

	once.Do(func() {
		repo = tariff.NewClientDistrictRateCloneBlRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) SttPasRepository() tariff.SttPasRepository {
	var once sync.Once
	var repo tariff.SttPasRepository

	once.Do(func() {
		repo = tariff.NewSttPasRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) DeviceRepository() deviceauth.DeviceRepository {
	var once sync.Once
	var repo deviceauth.DeviceRepository

	once.Do(func() {
		repo = deviceauth.NewDeviceRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) DeviceApprovalRepository() deviceauth.DeviceApprovalRepository {
	var once sync.Once
	var repo deviceauth.DeviceApprovalRepository

	once.Do(func() {
		repo = deviceauth.NewDeviceApprovalRepository(&r.cfg)
	})

	return repo
}

func (r moduleRepositoryRegistry) DeviceAuthLogRepository() deviceauth.DeviceAuthLogRepository {
	var once sync.Once
	var repo deviceauth.DeviceAuthLogRepository

	once.Do(func() {
		repo = deviceauth.NewDeviceAuthLogRepository(&r.cfg)
	})

	return repo
}
