package registry

import (
	"sync"

	"github.com/Lionparcel/horde/config/firebase"
	"github.com/Lionparcel/horde/registry/repository"

	"github.com/Lionparcel/horde/config"

	"github.com/Lionparcel/horde/src/usecase"
	"github.com/Lionparcel/horde/src/usecase/message_broker"
)

// UsecaseRegistry ...
type UsecaseRegistry interface {
	Account() usecase.Account
	Auth() usecase.Auth
	Token() usecase.Token
	Bulk() usecase.Bulk
	BulkV2() usecase.BulkV2
	Announcement() usecase.Announcement
	City() usecase.City
	Country() usecase.Country
	Notification() usecase.Notification
	District() usecase.District
	Embargo() usecase.Embargo
	Vendor() usecase.Vendor
	ProductType() usecase.ProductType
	Leg() usecase.Leg
	Route() usecase.Route
	Transport() usecase.Transport
	LimitRule() usecase.LimitRule
	Commodity() usecase.Commodity
	Location() usecase.Location
	ConfigurablePrice() usecase.ConfigurablePrice
	CommoditySurcharge() usecase.CommoditySurcharge
	Rates() usecase.Rates
	ExchangeRate() usecase.ExchangeRate
	GoodsTax() usecase.GoodsTax
	Client() usecase.Client
	Partner() usecase.Partner
	Customer() usecase.Customer
	Tariff() usecase.Tariff
	TariffToped() usecase.Tariff
	TariffBukalapak() usecase.Tariff
	EstimateSLA() usecase.EstimateSLA
	Driver() usecase.Driver
	Vehicle() usecase.Vehicle
	Bank() usecase.Bank
	GatewayTariff() usecase.GatewayTariff
	ConfigurableRule() usecase.ConfigurableRule
	CargoProductType() usecase.CargoProductType
	Airport() usecase.Airport
	BookingCommission() usecase.BookingCommission
	DeliveryVendor() usecase.DeliveryVendor
	Dtpol() usecase.Dtpol
	Message() usecase.Message
	MessageV2() usecase.MessageV2
	MessageYmWaPremium() usecase.MessageYmWaPremium
	Health() usecase.Health
	Kejarcuan() usecase.Kejarcuan
	Hub() usecase.Hub
	ActivityLog() usecase.ActivityLog
	PartnerLocation() usecase.PartnerLocation
	ResolutionCenter() usecase.ResolutionCenter
	ConfigMiniapp() usecase.ConfigMiniapp
	AccountV2() usecase.AccountV2
	PartnerV2() usecase.PartnerV2
	Permission() usecase.Permission
	LegV2() usecase.LegV2
	AuthV2() usecase.AuthV2
	RouteV2() usecase.RouteV2
	AuthIntegration() usecase.Auth
	PredefinedHoliday() usecase.PredefinedHoliday
	ConfigurableProgressiveCommission() usecase.ConfigurableProgressiveCommission
	BulkBookingTemplate() usecase.BulkBookingTemplate
	BaggingGroupLocation() usecase.BaggingGroupLocation
	YellowMessage() usecase.YellowMessage
	CargoConfiguration() usecase.CargoConfiguration
	ConfigurationBanner() usecase.ConfigurationBanner
	Banner() usecase.Banner
	PromoDiscountConfiguration() usecase.PromoDiscountConfiguration
	EventBatchProcessHandler() message_broker.EventBatchProcessHandler
	Region() usecase.Region
	Casecategory() usecase.CaseCategory
	ServiceDelay() usecase.ServiceDelay
	Event() usecase.Event
	CourierManagement() usecase.CourierManagement
	AccountNotification() usecase.AccountNotification
	RtcCityGroup() usecase.RtcCityGroup
	CodConfiguration() usecase.CodConfiguration
	BalanceMinusPenalty() usecase.BalanceMinusPenalty
	DexBucketTiketConfiguration() usecase.DexBucketTiketConfiguration
	DexFakeDexConfiguration() usecase.DexFakeDexConfiguration
	DexBucketTicketAccount() usecase.DexBucketTicketAccount
	CsAccountLog() usecase.CsAccountLog
	PickupSchedule() usecase.PickupSchedule
	InternationalDocumentConfiguration() usecase.InternationalDocumentConfiguration
	UserConsentConfiguration() usecase.UserConsentUsecase
	PickupCorporateAddress() usecase.PickupCorporateAddress
	PickupCommissionConfiguration() usecase.PickupCommissionConfiguration
	BulkSTTDetail() usecase.BulkSTTer
	CargoConfigurationSearchFlight() usecase.CargoConfigurationSearchFlightUC
	ConfigDfodPasti() usecase.ConfigDfodPasti
	CommodityCategory() usecase.CommodityCategory
	Otp() usecase.Otp
}

type usecaseRegistry struct {
	repo          repository.RepositoryRegistry
	cfg           config.Config
	moduleUsecase ModuleUsecaseRegistry
}

func (u *usecaseRegistry) CommodityCategory() usecase.CommodityCategory {
	var cc usecase.CommodityCategory
	var once sync.Once
	once.Do(func() {
		cc = usecase.NewCommodityCategoryUc(
			u.repo.CommodityCategory(),
		)
	})
	return cc
}

func (u *usecaseRegistry) PickupCommissionConfiguration() usecase.PickupCommissionConfiguration {
	var pcc usecase.PickupCommissionConfiguration
	var once sync.Once

	once.Do(func() {
		pcc = usecase.NewPickupCommissionConfigurationUseCase(
			usecase.SetPickupCommissionConfigurationRepo(u.repo.PickupCommissionConfigurationRepository()),
			usecase.SetPickupCommissionConfigurationConfig(u.cfg),
		)
	})

	return pcc
}

func (u *usecaseRegistry) PickupCorporateAddress() usecase.PickupCorporateAddress {
	var pca usecase.PickupCorporateAddress
	var once sync.Once

	once.Do(func() {
		pca = usecase.NewPickupCorporateAddressUseCase(
			usecase.SetPickupCorporateAddressRepo(u.repo.PickupCorporateAddressRepository()),
			usecase.SetPickupCorporateAddressClientRepo(u.repo.Client()),
			usecase.SetPickupCorporateAddressDistrictRepo(u.repo.District()),
			usecase.SetPickupCorporateAddressConfig(u.cfg),
			usecase.SetPickupCorporateAddressTimeRepo(u.repo.Time()),
		)
	})

	return pca
}

// NewUsecaseRegistry ...
func NewUsecaseRegistry(cfg config.Config, moduleUsecase ModuleUsecaseRegistry) (r UsecaseRegistry) {
	var ucRegistry usecaseRegistry
	var once sync.Once

	once.Do(func() {
		repoReg := repository.NewRepoRegistry(cfg)
		ucRegistry = usecaseRegistry{repo: repoReg, cfg: cfg, moduleUsecase: moduleUsecase}
	})

	return &ucRegistry
}

func (u *usecaseRegistry) Account() usecase.Account {
	var ucAccount usecase.Account
	var once sync.Once
	once.Do(func() {
		ucAccount = usecase.NewAccountUc(
			u.repo.Account(),
			u.repo.Client(),
			u.repo.Partner(),
			u.repo.Vendor(),
			u.cfg,
			u.repo.Hub(),
			u.repo.PartnerLocation(),
			u.repo.District(),
			u.repo.LimitRule(),
			u.repo.Cache(),
			u.repo.Time(),
			u.repo.DexBucketTicketAccount(),
			u.repo.CsAccountLog(),
			u.repo.Country(),
			u.repo.MetabaseRepository(),
			u.repo.LPTokenRepository(),
			u.repo.FlagManagementRepository(),
		)
	})
	return ucAccount
}

func (u *usecaseRegistry) ActivityLog() usecase.ActivityLog {
	var ucActivityLog usecase.ActivityLog
	var once sync.Once
	once.Do(func() {
		ucActivityLog = usecase.NewActivityLogUc(u.repo.ActivityLog())
	})
	return ucActivityLog
}

func (u *usecaseRegistry) Auth() usecase.Auth {
	var once sync.Once
	var ucAuth usecase.Auth
	once.Do(func() {
		ucAuth = usecase.NewAuthUc(
			u.repo.Account(),
			u.repo.ActivityLog(),
			u.repo.AccountToken(),
			u.repo.Partner(),
			u.repo.Client(),
			u.Token(),
			u.cfg,
			u.repo.RateVersion(),
			u.repo.PegasusRepository(),
			u.repo.CsAccountLog(),
			u.repo.LPTokenRepository(),
		)
	})
	return ucAuth
}

func (u *usecaseRegistry) Token() usecase.Token {
	var ucToken usecase.Token
	var once sync.Once
	once.Do(func() {
		ucToken = usecase.NewTokenUc(u.cfg, u.repo.AccountToken(), u.repo.Account())
	})
	return ucToken
}

func (u *usecaseRegistry) Bulk() usecase.Bulk {
	var ucBulk usecase.Bulk
	var once sync.Once
	once.Do(func() {
		ucBulk = usecase.NewBulkUc(usecase.BulkCtxConfig{
			Cfg:                           u.cfg,
			RoleRepo:                      u.repo.Role(),
			BulkRepo:                      u.repo.Bulk(),
			AccountRepo:                   u.repo.Account(),
			VendorRepo:                    u.repo.Vendor(),
			PartnerRepo:                   u.repo.Partner(),
			ClientRepo:                    u.repo.Client(),
			RouteLegRepo:                  u.repo.RouteLeg(),
			RouteRepo:                     u.repo.Route(),
			LegRepo:                       u.repo.Leg(),
			TransportRepo:                 u.repo.Transport(),
			CityRepo:                      u.repo.City(),
			DistrictRepo:                  u.repo.District(),
			RatesUc:                       u.Rates(),
			CommodityRepo:                 u.repo.Commodity(),
			RateVersionRepo:               u.repo.RateVersion(),
			CommoditySurchargeRepo:        u.repo.CommoditySurcharge(),
			CityClientRepo:                u.repo.CityClient(),
			DistrictClientRepo:            u.repo.DistrictClient(),
			ClientCityRateRepo:            u.repo.ClientCityRate(),
			ClientCityRateDetailRepo:      u.repo.ClientCityRateDetail(),
			ProductTypeRepo:               u.repo.ProductType(),
			ClientDistrictRateRepo:        u.repo.ClientDistrictRate(),
			HubRepo:                       u.repo.Hub(),
			PartnerLocationRepo:           u.repo.PartnerLocation(),
			EmbargoRepo:                   u.repo.Embargo(),
			ConfigurablePriceRepo:         u.repo.ConfigurablePrice(),
			BulkSttRepo:                   u.repo.BulkStt(),
			EmbargoUc:                     u.Embargo(),
			SttRepo:                       u.repo.Stt(),
			BookingCommissionRepo:         u.repo.BookingCommission(),
			TariffUc:                      u.Tariff(),
			EstimateSlaRepo:               u.repo.EstimateSla(),
			BulkBookingTemplateRepository: u.repo.BulkBookingTemplateRepo(),
			BaggingGroupLocationRepo:      u.repo.BaggingGroupLocation(),
			BankAccountRepo:               u.repo.BankAccount(),
			CargoConfig:                   u.repo.CargoConfiguration(),
			CacheRepo:                     u.repo.Cache(),
			CountryRepo:                   u.repo.Country(),
			ProductRepo:                   u.repo.ProductType(), // kemungkinan typo, sebelumnya ini ProductType, harusnya Product?
			CargoConfigurationRepo:        u.repo.CargoConfiguration(),
			PubsubRepo:                    u.repo.Pubsub(),
			EventRepo:                     u.repo.Event(),
			CargoReverseRepo:              u.repo.CargoReverse(),
			PartnerLogRepo:                u.repo.PartnerLog(),
			TimeRepo:                      u.repo.Time(),
			RegionRepo:                    u.repo.Region(),
			RegionCityRepo:                u.repo.RegionCity(),
			ReadyToCargo:                  u.repo.ReadyToCargo(),
			RtcCityGroupRepo:              u.repo.RtcCityGroup(),
			FlagManagementRepo:            u.repo.FlagManagementRepository(),
			PromoDiscountRepo:             u.repo.PromoDiscountConfigurationRepository(),
			PromoDiscountConfig:           u.PromoDiscountConfiguration(),
			ReasonRepo:                    u.repo.ReasonRepo(),
			CustomProcessRepo:             u.repo.CustomProcessRepo(),
			DistrictVendorRepo:            u.repo.DistrictVendor(),
			AlgoRepo:                      u.repo.AlgoRepo(),
			CustomerRepo:                  u.repo.Customer(),
			InternationalDocConfig:        u.repo.InternationalDocumentConfiguration(),
			ClientDistrictRateDetailRepo:  u.repo.ClientDistrictRateDetail(),
			RepLagRepo:                    u.repo.RepLag(),
			BulkSttDetailRepo:             u.repo.BulkSttDetail(),
			FirebaseRepo:                  u.repo.Firebase(firebase.NewFirebaseCloudMessaging()),
			CodConfigurationRepo:          u.repo.CodConfiguration(),
			ServiceDelayUc:                u.ServiceDelay(),
			LimitterRepo:                  u.repo.LimitterRepository(),
			CommodityCategoryRepo:         u.repo.CommodityCategory(),
		})
	})

	return ucBulk
}

func (u *usecaseRegistry) BulkV2() usecase.BulkV2 {
	var ucBulkV2 usecase.BulkV2
	var once sync.Once
	once.Do(func() {
		ucBulkV2 = usecase.NewBulkV2Uc(
			u.cfg,
			u.repo.Role(),
			u.repo.Bulk(),
			u.repo.Account(),
			u.repo.Vendor(),
			u.repo.Partner(),
			u.repo.Client(),
			u.repo.RouteLeg(),
			u.repo.Route(),
			u.repo.Leg(),
			u.repo.Transport(),
			u.repo.City(),
			u.repo.District(),
			u.Rates(),
			u.repo.Commodity(),
			u.repo.RateVersion(),
			u.repo.CommoditySurcharge(),
			u.repo.CityClient(),
			u.repo.DistrictClient(),
			u.repo.ClientCityRate(),
			u.repo.ClientCityRateDetail(),
			u.repo.ProductType(),
			u.repo.ClientDistrictRate(),
			u.repo.Hub(),
			u.repo.PartnerLocation(),
			u.repo.Embargo(),
			u.repo.ConfigurablePrice(),
			u.repo.BulkStt(),
			u.Embargo(),
			u.repo.Stt(),
			u.repo.BookingCommission(),
			u.Tariff(),
			u.repo.EstimateSla(),
			u.Bulk(),
			u.repo.Time(),
			u.repo.CommodityCategory(),
		)
	})

	return ucBulkV2
}

func (u *usecaseRegistry) Announcement() usecase.Announcement {
	var once sync.Once
	var ucAnnouncement usecase.Announcement

	once.Do(func() {
		ucAnnouncement = usecase.NewAnnouncementUc(u.repo.Announcement(), u.repo.Account(), u.cfg,
			u.repo.FlagManagementRepository(),
		)
	})
	return ucAnnouncement
}

func (u *usecaseRegistry) City() usecase.City {
	var once sync.Once
	var ucCity usecase.City

	once.Do(func() {
		ucCity = usecase.NewCityUc(
			u.repo.City(),
			u.repo.CityClient(),
			u.repo.Account(),
			u.repo.District(),
			u.repo.Cache(),
			u.cfg, u.repo.Country(),
			u.repo.RegionCity(),
			u.repo.Region(),
		)
	})
	return ucCity
}

func (u *usecaseRegistry) Country() usecase.Country {
	var once sync.Once
	var ucCountry usecase.Country

	once.Do(func() {
		ucCountry = usecase.NewCountryUc(u.repo.Country(), u.repo.Account())
	})
	return ucCountry
}

func (u *usecaseRegistry) Notification() usecase.Notification {
	var once sync.Once
	var ucNotification usecase.Notification

	once.Do(func() {
		ucNotification = usecase.NewNotificationUc(usecase.NotificationParam{AnnouncementRepo: u.repo.Announcement(), AccountRepo: u.repo.Account(), TimeRepo: u.repo.Time(), PartnerLogRepo: u.repo.PartnerLog(), FirebaseRepo: u.repo.Firebase(firebase.NewFirebaseCloudMessaging()), PartnerRepo: u.repo.Partner(), AccountNotificationRepo: u.repo.AccountNotification()})
	})

	return ucNotification
}

func (u *usecaseRegistry) District() usecase.District {
	var once sync.Once
	var ucDistrict usecase.District

	once.Do(func() {
		ucDistrict = usecase.NewDistrictUc(
			u.repo.District(),
			u.repo.Account(),
			u.repo.Cache(),
			u.repo.DistrictClient(),
			u.repo.Client(),
			u.cfg,
			u.repo.Country(),
			u.repo.DistrictVendor(),
			u.repo.City(),
			u.repo.PartnerLocation(),
		)
	})
	return ucDistrict
}

func (u *usecaseRegistry) Embargo() usecase.Embargo {
	var once sync.Once
	var ucEmbargo usecase.Embargo

	once.Do(func() {
		ucEmbargo = usecase.NewEmbargoUc(u.repo.Embargo(), u.repo.ProductType(), u.repo.City(), u.repo.Commodity(), u.repo.Account(), u.repo.Commodity(), u.repo.Cache(), u.repo.Time())
	})
	return ucEmbargo
}

func (u *usecaseRegistry) ProductType() usecase.ProductType {
	var once sync.Once
	var ucProductType usecase.ProductType

	once.Do(func() {
		ucProductType = usecase.NewProductTypeUc(u.repo.ProductType(), u.repo.Account(), u.repo.CargoProductType())
	})
	return ucProductType
}

func (u *usecaseRegistry) Vendor() usecase.Vendor {
	var once sync.Once
	var ucVendor usecase.Vendor

	once.Do(func() {
		ucVendor = usecase.NewVendorUc(u.repo.Vendor(), u.repo.Transport(), u.repo.Leg())
	})
	return ucVendor
}

func (u *usecaseRegistry) Leg() usecase.Leg {
	var once sync.Once
	var ucLeg usecase.Leg

	once.Do(func() {
		ucLeg = usecase.NewLegUc(u.cfg, u.repo.Leg(), u.repo.Account(), u.repo.City(), u.repo.District(), u.repo.RouteLeg(), u.repo.Time())
	})
	return ucLeg
}

func (u *usecaseRegistry) Route() usecase.Route {
	var once sync.Once
	var ucRoute usecase.Route

	once.Do(func() {
		ucRoute = usecase.NewRouteUc(u.repo.Route(), u.repo.RouteLeg(), u.repo.City(), u.repo.District(), u.repo.Account(), u.repo.EstimateSla(), u.repo.Cache(), u.repo.Time())
	})
	return ucRoute
}

func (u *usecaseRegistry) RouteV2() usecase.RouteV2 {
	var once sync.Once
	var ucRoute usecase.RouteV2

	once.Do(func() {
		ucRoute = usecase.NewRouteV2Uc(u.repo.Route(), u.repo.RouteLeg(), u.repo.City(), u.repo.District(), u.repo.Account(), u.repo.EstimateSla(), u.repo.Cache())
	})
	return ucRoute
}

func (u *usecaseRegistry) Transport() usecase.Transport {
	var once sync.Once
	var ucTransport usecase.Transport

	once.Do(func() {
		ucTransport = usecase.NewTransportUc(u.repo.Transport(), u.repo.Account(), u.repo.Vendor())
	})
	return ucTransport
}

func (u *usecaseRegistry) LimitRule() usecase.LimitRule {
	var once sync.Once
	var ucLimitRule usecase.LimitRule

	once.Do(func() {
		ucLimitRule = usecase.NewLimitRuleUc(u.repo.LimitRule(), u.repo.Account(), u.repo.PartnerLocation())
	})
	return ucLimitRule
}

func (u *usecaseRegistry) Rates() usecase.Rates {
	var once sync.Once
	var ucRates usecase.Rates

	once.Do(func() {
		ucRates = usecase.NewRatesUc(
			u.repo.CityRate(),
			u.repo.CityRateDetail(),
			u.repo.City(),
			u.repo.District(),
			u.repo.Client(),
			u.repo.RateVersion(),
			u.repo.DistrictRate(),
			u.repo.DistrictRateDetail(),
			u.repo.RateActivityLog(),
			u.repo.Account(),
			u.repo.ProductType(),
			u.cfg,
			u.repo.ClientCityRateDetail(),
			u.repo.ClientCityRate(),
			u.repo.Bulk(),
			u.repo.Time(),
			u.repo.Event(),
			u.repo.PartnerLog(),
			u.repo.FlagManagementRepository(),
			u.repo.RepLag(),
			u.repo.Cache(),
		)
	})

	return ucRates
}

func (u *usecaseRegistry) Commodity() usecase.Commodity {
	var once sync.Once
	var ucCommodity usecase.Commodity

	once.Do(func() {
		ucCommodity = usecase.NewCommodityUc(&u.cfg, u.repo.Commodity(), u.repo.Account(), u.repo.Cache(), u.repo.CommodityCategory())
	})
	return ucCommodity
}

func (u *usecaseRegistry) Location() usecase.Location {
	var once sync.Once
	var ucLocation usecase.Location

	once.Do(func() {
		ucLocation = usecase.NewLocationUc(u.repo.District(), u.repo.City())
	})
	return ucLocation
}

func (u *usecaseRegistry) ConfigurablePrice() usecase.ConfigurablePrice {
	var once sync.Once
	var ucConfigurablePrice usecase.ConfigurablePrice

	once.Do(func() {
		ucConfigurablePrice = usecase.NewConfigurablePriceUc(
			u.repo.ConfigurablePrice(),
			u.repo.Account(),
			u.repo.Commodity(),
			u.repo.ProductType(),
			u.repo.City(),
			u.repo.Cache(),
			u.Permission(),
			u.repo.Time(),
			u.repo.ConfigurableWoodpackingCity(),
		)
	})
	return ucConfigurablePrice
}

func (u *usecaseRegistry) CommoditySurcharge() usecase.CommoditySurcharge {
	var once sync.Once
	var ucCommoditySurcharge usecase.CommoditySurcharge

	once.Do(func() {
		ucCommoditySurcharge = usecase.NewCommoditySurchargeUc(u.repo.CommoditySurcharge(), u.repo.City(), u.repo.Commodity(), u.repo.Account(), u.Permission(), u.repo.Time())
	})
	return ucCommoditySurcharge
}

func (u *usecaseRegistry) CargoConfiguration() usecase.CargoConfiguration {
	var once sync.Once
	var ucCargoConfiguration usecase.CargoConfiguration

	once.Do(func() {
		ucCargoConfiguration = usecase.NewCargoConfigurationUc(
			&u.cfg,
			u.repo.CargoConfiguration(),
			u.repo.Account(),
			u.repo.City(),
			u.repo.ReadyToCargo(),
			u.repo.CutOffTimeRepository(),
			u.repo.Commodity(),
			u.repo.ProductType(),
			u.repo.PartnerLog(),
			u.repo.Route(),
			u.repo.RouteLeg(),
			u.repo.RtcCityGroup(),
			u.repo.Time(),
			u.repo.Client(),
			u.repo.Cache(),
		)
	})

	return ucCargoConfiguration
}

func (u *usecaseRegistry) Client() usecase.Client {
	var once sync.Once
	var ucClient usecase.Client

	once.Do(func() {
		ucClient = usecase.NewClientUc(
			u.repo.Client(),
			u.repo.CityRate(),
			u.repo.ClientCityRate(),
			u.repo.CityRateDetail(),
			u.repo.ClientCityRateDetail(),
			u.repo.DistrictRate(),
			u.repo.ClientDistrictRate(),
			u.repo.DistrictRateDetail(),
			u.repo.ClientDistrictRateDetail(),
			u.Bulk(),
			u.repo.RateVersion(),
			u.repo.Account(),
			u.repo.Bulk(),
			u.repo.District(),
			u.cfg,
			u.repo.Partner(),
			u.repo.BankAccount(),
			u.repo.City(),
			u.repo.PartnerLog(),
			u.repo.ProductType(),
			u.repo.Cache(),
			u.repo.OdooContactRepo(),
			u.repo.Time(),
			u.repo.FlagManagementRepository(),
			u.repo.RepLag(),
		)
	})
	return ucClient
}

func (u *usecaseRegistry) ExchangeRate() usecase.ExchangeRate {
	var once sync.Once
	var ucExchangeRate usecase.ExchangeRate

	once.Do(func() {
		ucExchangeRate = usecase.NewExchangeRateUc(u.repo.ExchangeRate(), u.repo.Account(), u.Permission(), u.repo.Country())
	})
	return ucExchangeRate
}
func (u *usecaseRegistry) GoodsTax() usecase.GoodsTax {
	var once sync.Once
	var ucGoodsTax usecase.GoodsTax

	once.Do(func() {
		ucGoodsTax = usecase.NewGoodsTaxUc(u.repo.GoodsTax(), u.repo.Account(), u.repo.Commodity(), u.repo.City(), u.Permission())
	})
	return ucGoodsTax
}

func (u *usecaseRegistry) Partner() usecase.Partner {
	var once sync.Once
	var ucPartner usecase.Partner

	once.Do(func() {
		ucPartner = usecase.NewPartnerUc(
			u.cfg,
			u.repo.Partner(),
			u.repo.Client(),
			u.repo.PartnerLocation(),
			u.repo.District(),
			u.repo.City(),
			u.repo.Account(),
			u.repo.BankAccount(),
			u.repo.Bank(),
			u.repo.BulkDownloadRepository(),
			u.repo.PartnerLog(),
			u.repo.Wallet(),
			u.repo.AlgoRepo(),
			u.repo.FlagManagementRepository(),
			u.repo.Country(),
			u.repo.PartnerSaldoDepositHistory(),
			u.repo.MetabaseRepository(),
			u.repo.TransactionRepository(),
			u.repo.Stt(),
			u.repo.PickUpManifestRepository(),
		)
	})

	return ucPartner
}

func (u *usecaseRegistry) Tariff() usecase.Tariff {
	var once sync.Once
	var ucTariff usecase.Tariff

	once.Do(func() {
		ucTariff = usecase.NewTariffUc(u.repo.ProductType(),
			u.repo.City(),
			u.repo.District(),
			u.repo.Commodity(),
			u.repo.Account(),
			u.repo.CityRate(),
			u.repo.CityRateDetail(),
			u.repo.DistrictRate(),
			u.repo.DistrictRateDetail(),
			u.repo.RateVersion(),
			u.repo.ClientCityRate(),
			u.repo.ClientCityRateDetail(),
			u.repo.ClientDistrictRate(),
			u.repo.ClientDistrictRateDetail(),
			u.repo.CommoditySurcharge(),
			u.repo.ConfigurablePrice(),
			u.repo.GoodsTax(),
			u.repo.ExchangeRate(),
			u.repo.Client(),
			u.Embargo(),
			u.ConfigurableRule(),
			u.repo.ElexysIntegration(),
			u.repo.SttPas(),
			u.cfg,
			u.EstimateSLA(),
			u.repo.PartnerLocation(),
			u.repo.Time(),
			u.repo.DistrictClient(),
			u.repo.Cache(),
			u.repo.PromoDiscountConfigurationRepository(),
			u.repo.CodConfiguration(),
			u.repo.BookingCommission(),
			u.repo.BookingCommissionTier(),
			u.repo.Country(),
			u.repo.ConfigurableWoodpackingCity(),
			u.repo.ConfigurableInsuranceTierDetail(),
			u.repo.FlagManagementRepository(),
			u.moduleUsecase.TariffUc(),
			u.moduleUsecase.TariffTokopedia(),
			u.moduleUsecase.TariffBukalapak(),
		)
	})

	return ucTariff
}

func (u *usecaseRegistry) TariffToped() usecase.Tariff {
	var once sync.Once
	var ucTariff usecase.Tariff

	once.Do(func() {
		ucTariff = usecase.NewTariffUc(u.repo.ProductType(),
			u.repo.City(),
			u.repo.District(),
			u.repo.Commodity(),
			u.repo.Account(),
			u.repo.CityRate(),
			u.repo.CityRateDetail(),
			u.repo.DistrictRate(),
			u.repo.DistrictRateDetail(),
			u.repo.RateVersionClone(),          //DB Horde Clone Toped
			u.repo.ClientCityRateClone(),       //DB Horde Clone Toped
			u.repo.ClientCityRateDetailClone(), //DB Horde Clone Toped
			u.repo.ClientDistrictRate(),
			u.repo.ClientDistrictRateDetail(),
			u.repo.CommoditySurcharge(),
			u.repo.ConfigurablePrice(),
			u.repo.GoodsTax(),
			u.repo.ExchangeRate(),
			u.repo.ClientClone(), //DB Horde Clone Toped
			u.Embargo(),
			u.ConfigurableRule(),
			u.repo.ElexysIntegration(),
			u.repo.SttPas(),
			u.cfg,
			u.EstimateSLA(),
			u.repo.PartnerLocation(),
			u.repo.Time(),
			u.repo.DistrictClient(),
			u.repo.Cache(),
			u.repo.PromoDiscountConfigurationRepository(),
			u.repo.CodConfiguration(),
			u.repo.BookingCommission(),
			u.repo.BookingCommissionTier(),
			u.repo.Country(),
			u.repo.ConfigurableWoodpackingCity(),
			u.repo.ConfigurableInsuranceTierDetail(),
			u.repo.FlagManagementRepository(),
			u.moduleUsecase.TariffUc(),
			u.moduleUsecase.TariffTokopedia(),
			u.moduleUsecase.TariffBukalapak(),
		)
	})

	return ucTariff
}

func (u *usecaseRegistry) TariffBukalapak() usecase.Tariff {
	var once sync.Once
	var ucTariff usecase.Tariff

	once.Do(func() {
		ucTariff = usecase.NewTariffUc(u.repo.ProductType(),
			u.repo.City(),
			u.repo.District(),
			u.repo.Commodity(),
			u.repo.Account(),
			u.repo.CityRate(),
			u.repo.CityRateDetail(),
			u.repo.DistrictRate(),
			u.repo.DistrictRateDetail(),
			u.repo.RateVersionCloneBL(),          //DB Horde Clone Bukalapak
			u.repo.ClientCityRateCloneBL(),       //DB Horde Clone Bukalapak
			u.repo.ClientCityRateDetailCloneBL(), //DB Horde Clone Bukalapak
			u.repo.ClientDistrictRate(),
			u.repo.ClientDistrictRateDetail(),
			u.repo.CommoditySurcharge(),
			u.repo.ConfigurablePrice(),
			u.repo.GoodsTax(),
			u.repo.ExchangeRate(),
			u.repo.ClientCloneBL(), //DB Horde Clone Bukalapak
			u.Embargo(),
			u.ConfigurableRule(),
			u.repo.ElexysIntegration(),
			u.repo.SttPas(),
			u.cfg,
			u.EstimateSLA(),
			u.repo.PartnerLocation(),
			u.repo.Time(),
			u.repo.DistrictClient(),
			u.repo.Cache(),
			u.repo.PromoDiscountConfigurationRepository(),
			u.repo.CodConfiguration(),
			u.repo.BookingCommission(),
			u.repo.BookingCommissionTier(),
			u.repo.Country(),
			u.repo.ConfigurableWoodpackingCity(),
			u.repo.ConfigurableInsuranceTierDetail(),
			u.repo.FlagManagementRepository(),
			u.moduleUsecase.TariffUc(),
			u.moduleUsecase.TariffTokopedia(),
			u.moduleUsecase.TariffBukalapak(),
		)
	})

	return ucTariff
}

func (u *usecaseRegistry) Customer() usecase.Customer {
	var ucCustomer usecase.Customer
	var once sync.Once
	once.Do(func() {
		ucCustomer = usecase.NewCustomerUc(usecase.CustomerCtx{
			CustomerRepo:  u.repo.Customer(),
			PartnerRepo:   u.repo.Partner(),
			ClientRepo:    u.repo.Client(),
			Cfg:           u.cfg,
			PartnerLog:    u.repo.PartnerLog(),
			DistrictRepo:  u.repo.District(),
			CommodityRepo: u.repo.Commodity(),
		})
	})
	return ucCustomer
}

func (u *usecaseRegistry) EstimateSLA() usecase.EstimateSLA {
	var ucEstimatedSLA usecase.EstimateSLA
	var once sync.Once

	once.Do(func() {
		ucEstimatedSLA = usecase.NewEstimateSLA(u.repo.EstimateSla(), u.repo.District())
	})

	return ucEstimatedSLA
}

func (u *usecaseRegistry) Vehicle() usecase.Vehicle {
	var ucVehicle usecase.Vehicle
	var once sync.Once

	once.Do(func() {
		ucVehicle = usecase.NewVehicleUc(
			u.repo.Vehicle(),
			u.repo.Account(),
		)
	})

	return ucVehicle
}

func (u *usecaseRegistry) Driver() usecase.Driver {
	var ucDriver usecase.Driver
	var once sync.Once
	once.Do(func() {
		ucDriver = usecase.NewDriverUc(u.repo.Driver(), u.repo.Partner(), u.repo.Account())
	})
	return ucDriver
}

func (u *usecaseRegistry) Bank() usecase.Bank {
	var ucBank usecase.Bank
	var once sync.Once
	once.Do(func() {
		ucBank = usecase.NewBankUc(
			u.repo.Bank(),
		)
	})
	return ucBank
}

func (u *usecaseRegistry) GatewayTariff() usecase.GatewayTariff {
	var ucGatewayTariff usecase.GatewayTariff
	var once sync.Once
	once.Do(func() {
		ucGatewayTariff = usecase.NewGatewayTariff(u.Tariff(), u.repo.City(), u.repo.District(), u.repo.ProductType(), u.repo.Commodity(), u.repo.ConfigurablePrice(), u.repo.Embargo(), u.Embargo(), u.repo.CodConfiguration(), u.repo.ConfigurableWoodpackingCity())
	})

	return ucGatewayTariff
}

func (u *usecaseRegistry) ConfigurableRule() usecase.ConfigurableRule {
	var ucConfigurableRule usecase.ConfigurableRule
	var once sync.Once
	once.Do(func() {
		ucConfigurableRule = usecase.NewConfigurableRule(u.repo.ConfigurableRule(), u.cfg)
	})

	return ucConfigurableRule
}

func (u *usecaseRegistry) CargoProductType() usecase.CargoProductType {
	var cargoProductTypeUc usecase.CargoProductType
	var once sync.Once
	once.Do(func() {
		cargoProductTypeUc = usecase.NewCargoProductTypeUc(u.repo.CargoProductType())
	})

	return cargoProductTypeUc
}

func (u *usecaseRegistry) Airport() usecase.Airport {
	var airportUc usecase.Airport
	var once sync.Once
	once.Do(func() {
		airportUc = usecase.NewAirportUc(u.repo.Airport())
	})

	return airportUc
}

func (u *usecaseRegistry) BookingCommission() usecase.BookingCommission {
	var bookingCommissionUc usecase.BookingCommission
	var once sync.Once
	once.Do(func() {
		bookingCommissionUc = usecase.NewBookingCommissionUc(
			u.repo.BookingCommissionTier(),
			u.repo.BookingCommission(),
			u.repo.Account(),
			u.repo.ProductType(),
			u.repo.Bulk(),
			u.repo.Commodity(),
			u.repo.City(),
			u.cfg,
			u.Bulk(),
			u.repo.Time(),
		)
	})

	return bookingCommissionUc
}

// DeliveryVendor ...
func (u *usecaseRegistry) DeliveryVendor() usecase.DeliveryVendor {
	var deliveryVendorUc usecase.DeliveryVendor
	var once sync.Once

	once.Do(func() {
		deliveryVendorUc = usecase.NewDeliveryVendorUc(
			u.repo.DeliveryVendor(),
			u.repo.Partner(),
			u.repo.Account(),
			u.repo.Time(),
		)
	})

	return deliveryVendorUc
}

func (u *usecaseRegistry) Dtpol() usecase.Dtpol {
	var dtpolUc usecase.Dtpol
	var once sync.Once
	once.Do(func() {
		dtpolUc = usecase.NewDtpolUc(
			u.repo.Dtpol(),
			u.repo.PredefinedHoliday(),
			u.repo.District(),
			u.repo.ServiceDelay(),
			u.Permission(),
			u.repo.Time(),
		)
	})

	return dtpolUc
}

func (u *usecaseRegistry) Message() usecase.Message {
	var messageUc usecase.Message
	var once sync.Once
	once.Do(func() {
		messageUc = usecase.NewMessageUc(u.repo.Message(), u.repo.Account(), u.repo.City(), u.repo.Partner(), u.repo.Client(), u.repo.ProductType())
	})

	return messageUc
}

func (u *usecaseRegistry) MessageV2() usecase.MessageV2 {
	var messageUc usecase.MessageV2
	var once sync.Once
	once.Do(func() {
		messageUc = usecase.NewMessageUcV2(u.repo.Message(), u.repo.Account(), u.repo.City(), u.repo.Partner(), u.repo.Client(), u.repo.ProductType(), u.Message(), u.cfg)
	})

	return messageUc
}

func (u *usecaseRegistry) MessageYmWaPremium() usecase.MessageYmWaPremium {
	var messageYmWaPremiumUc usecase.MessageYmWaPremium
	var once sync.Once
	once.Do(func() {
		messageYmWaPremiumUc = usecase.NewMessageYmWaPremiumUc(
			u.repo.MessageYmWAPremium(),
		)
	})

	return messageYmWaPremiumUc
}

func (u *usecaseRegistry) Health() usecase.Health {
	var healthUc usecase.Health
	var once sync.Once
	once.Do(func() {
		healthUc = usecase.NewHealthUc(
			u.repo.Health(),
			u.cfg,
		)
	})

	return healthUc
}

func (u *usecaseRegistry) Kejarcuan() usecase.Kejarcuan {
	var kejarcuanUc usecase.Kejarcuan
	var once sync.Once
	once.Do(func() {
		kejarcuanUc = usecase.NewKejarcuanUc(
			u.repo.Kejarcuan(),
			u.repo.Account(),
			u.cfg,
		)
	})

	return kejarcuanUc
}

func (u *usecaseRegistry) PartnerLocation() usecase.PartnerLocation {
	var once sync.Once
	var ucPartnerLocation usecase.PartnerLocation
	once.Do(func() {
		ucPartnerLocation = usecase.NewPartnerLocationUc(u.repo.PartnerLocation())
	})
	return ucPartnerLocation
}

func (u *usecaseRegistry) ResolutionCenter() usecase.ResolutionCenter {
	var once sync.Once
	var ucResolutionCenter usecase.ResolutionCenter
	once.Do(func() {
		ucResolutionCenter = usecase.NewResolutionCenter(u.repo.ResolutionCenter())
	})
	return ucResolutionCenter
}

func (u *usecaseRegistry) ConfigMiniapp() usecase.ConfigMiniapp {
	var once sync.Once
	var uc usecase.ConfigMiniapp
	once.Do(func() {
		uc = usecase.NewConfigMiniappUc(
			u.repo.ConfigMiniapp(),
		)
	})
	return uc
}

func (u *usecaseRegistry) AccountV2() usecase.AccountV2 {
	var ucAccount usecase.AccountV2
	var once sync.Once
	once.Do(func() {
		ucAccount = usecase.NewAccountV2Uc(
			u.repo.Account(),
			u.repo.Client(),
			u.repo.Partner(),
			u.repo.Vendor(),
			u.cfg,
			u.repo.Hub(),
			u.repo.PartnerLocation(),
			u.repo.District(),
			u.repo.LimitRule(),
			u.repo.Cache(),
			u.repo.CsAccountLog(),
			u.repo.Country(),
		)
	})
	return ucAccount
}

func (u *usecaseRegistry) PartnerV2() usecase.PartnerV2 {
	var once sync.Once
	var ucPartnerV2 usecase.PartnerV2

	once.Do(func() {
		ucPartnerV2 = usecase.NewPartnerV2Uc(u.repo.Partner(), u.repo.PartnerLog(), u.Partner(), u.cfg)
	})

	return ucPartnerV2
}

func (u *usecaseRegistry) Permission() usecase.Permission {
	var ucPermission usecase.Permission
	var once sync.Once
	once.Do(func() {
		ucPermission = usecase.NewPermissionRepository(u.repo.Account(), u.repo.Cache(), u.cfg)
	})
	return ucPermission
}

func (u *usecaseRegistry) LegV2() usecase.LegV2 {
	var once sync.Once
	var ucLegV2 usecase.LegV2

	once.Do(func() {
		ucLegV2 = usecase.NewLegV2Uc(u.cfg, u.repo.Leg(), u.repo.Account(), u.repo.City(), u.repo.District(), u.repo.RouteLeg())
	})
	return ucLegV2
}

func (u *usecaseRegistry) AuthV2() usecase.AuthV2 {
	var once sync.Once
	var ucAuthV2 usecase.AuthV2
	once.Do(func() {
		ucAuthV2 = usecase.NewAuthV2Uc(
			u.repo.Account(),
			u.repo.ActivityLog(),
			u.repo.AccountToken(),
			u.repo.Partner(),
			u.Token(),
			u.repo.Client(),
			u.repo.CsAccountLog(),
			u.cfg,
			u.repo.OtpRepository(),
			u.repo.PartnerLog(),
			u.repo.FlagManagementRepository(),
			u.Otp(),
		)
	})
	return ucAuthV2
}

func (u *usecaseRegistry) AuthIntegration() usecase.Auth {
	var once sync.Once
	var ucAuthIntegration usecase.Auth
	once.Do(func() {
		ucAuthIntegration = usecase.NewAuthUc(
			u.repo.Account(),
			u.repo.ActivityLog(),
			u.repo.AccountToken(),
			u.repo.Partner(),
			u.repo.Client(),
			u.Token(),
			u.cfg,
			u.repo.RateVersion(),
			u.repo.PegasusRepository(),
			u.repo.CsAccountLog(),
			u.repo.LPTokenRepository(),
		)
	})
	return ucAuthIntegration
}

func (u *usecaseRegistry) PredefinedHoliday() usecase.PredefinedHoliday {
	var once sync.Once
	var ucPredefinedHoliday usecase.PredefinedHoliday
	once.Do(func() {
		ucPredefinedHoliday = usecase.NewPredefinedHolidayUC(
			u.repo.PredefinedHoliday(),
			u.repo.FlagManagementRepository(),
		)
	})
	return ucPredefinedHoliday
}

func (u *usecaseRegistry) ConfigurableProgressiveCommission() usecase.ConfigurableProgressiveCommission {
	var once sync.Once
	var uc usecase.ConfigurableProgressiveCommission
	once.Do(func() {
		uc = usecase.NewConfigurableProgressiveCommissionUc(
			u.cfg,
			u.repo.PartnerLog(),
			u.repo.ConfigurableProgressiveCommissionRepository(),
			u.repo.ConfigurableProgressiveCommissionDetailRepository(),
			u.repo.Partner(),
			u.repo.PartnerLocation(),
			u.repo.Pubsub(),
			u.repo.Time(),
			u.repo.Account(),
		)
	})
	return uc
}

func (u *usecaseRegistry) BulkBookingTemplate() usecase.BulkBookingTemplate {
	var bulkBookingTemplate usecase.BulkBookingTemplate
	var once sync.Once

	once.Do(func() {
		bulkBookingTemplate = usecase.NewBulkBookingTemplate(
			u.cfg,
			u.repo.BulkBookingTemplateRepo(),
			u.repo.Client(),
			u.repo.Account(),
			u.repo.Time(),
		)
	})

	return bulkBookingTemplate
}

func (u *usecaseRegistry) YellowMessage() usecase.YellowMessage {
	var yellowMessage usecase.YellowMessage
	var once sync.Once

	once.Do(func() {
		yellowMessage = usecase.NewYellowMessagaUc(
			u.cfg,
			u.repo.Account(),
		)
	})

	return yellowMessage
}

func (u *usecaseRegistry) BaggingGroupLocation() usecase.BaggingGroupLocation {
	var baggingGroupLocationUc usecase.BaggingGroupLocation
	var once sync.Once

	once.Do(func() {
		baggingGroupLocationUc = usecase.NewBaggingGroupLocationUc(
			&u.cfg,
			u.repo.BaggingGroupLocation(),
			u.repo.Partner(),
			u.repo.PartnerLocation(),
			u.repo.Account(),
		)
	})

	return baggingGroupLocationUc
}

func (u *usecaseRegistry) ConfigurationBanner() usecase.ConfigurationBanner {
	var configurationBannerUc usecase.ConfigurationBanner
	var once sync.Once

	once.Do(func() {
		configurationBannerUc = usecase.NewConfigurationBannerUc(
			&u.cfg,
			u.repo.ConfigurationBannerRepo(),
			u.repo.Account(),
			u.repo.FlagManagementRepository(),
		)
	})

	return configurationBannerUc
}

func (u *usecaseRegistry) Banner() usecase.Banner {
	var bannerUc usecase.Banner
	var once sync.Once

	once.Do(func() {
		bannerUc = usecase.NewBannerUc(
			&u.cfg,
			u.repo.ConfigurationBannerRepo(),
			u.repo.Account(),
			u.repo.FlagManagementRepository(),
		)
	})

	return bannerUc
}

func (u *usecaseRegistry) PromoDiscountConfiguration() usecase.PromoDiscountConfiguration {
	var promoDiscountConfiguration usecase.PromoDiscountConfiguration
	var once sync.Once

	once.Do(func() {
		promoDiscountConfiguration = usecase.NewPromoDiscountConfigurationUc(
			u.repo.PromoDiscountConfigurationRepository(),
			u.repo.Time(),
			u.cfg,
			u.repo.City(),
			u.ConfigurableRule(),
			u.repo.ProductType(),
		)
	})

	return promoDiscountConfiguration
}

func (u *usecaseRegistry) EventBatchProcessHandler() message_broker.EventBatchProcessHandler {
	var ucEventBatchProcessHandler message_broker.EventBatchProcessHandler
	var once sync.Once
	once.Do(func() {
		ucEventBatchProcessHandler = message_broker.NewEventBatchProcessHandler(
			u.Event(),
			u.repo.PartnerLog(),
			u.cfg,
		)
	})
	return ucEventBatchProcessHandler
}

func (u *usecaseRegistry) Event() usecase.Event {
	var eventUc usecase.Event
	var once sync.Once

	once.Do(func() {
		eventUc = usecase.NewEventUc(
			u.cfg,
			u.repo.Event(),
			u.Bulk(),
			u.Rates(),
			u.repo.Cache(),
			u.repo.Time(),
			u.repo.PartnerLog(),
			u.repo.Bulk(),
			u.repo.RateVersion(),
		)
	})

	return eventUc
}

func (u *usecaseRegistry) Hub() usecase.Hub {
	var HubUc usecase.Hub
	var once sync.Once

	once.Do(func() {
		HubUc = usecase.NewHubUc(
			u.cfg,
			u.repo.Hub(),
			u.repo.Partner(),
		)
	})

	return HubUc
}

func (u *usecaseRegistry) Region() usecase.Region {
	var RegionUc usecase.Region
	var once sync.Once

	once.Do(func() {
		RegionUc = usecase.NewRegionUc(
			u.repo.RegionCity(),
			u.repo.Region(),
		)
	})

	return RegionUc
}

func (u *usecaseRegistry) Casecategory() usecase.CaseCategory {
	var CaseCategoryUc usecase.CaseCategory
	var once sync.Once

	once.Do(func() {
		CaseCategoryUc = usecase.NewCaseCategoryUc(
			u.cfg,
			u.repo.CaseCategory(),
		)
	})

	return CaseCategoryUc
}

func (u *usecaseRegistry) ServiceDelay() usecase.ServiceDelay {
	var ServiceDelayUc usecase.ServiceDelay
	var once sync.Once

	once.Do(func() {
		ServiceDelayUc = usecase.NewServiceDelayUc(
			u.repo.ServiceDelay(),
			u.repo.District(),
			u.repo.City(),
			u.repo.Time(),
		)
	})

	return ServiceDelayUc
}

func (u *usecaseRegistry) CourierManagement() usecase.CourierManagement {
	var CourierManagementUc usecase.CourierManagement
	var once sync.Once

	once.Do(func() {
		CourierManagementUc = usecase.NewCourierManagementUc(
			u.repo.CourierManagement(),
			u.repo.Partner(),
			u.repo.AlgoRepo(),
		)
	})

	return CourierManagementUc
}

func (u *usecaseRegistry) AccountNotification() usecase.AccountNotification {
	var AccountNotificationUc usecase.AccountNotification
	var once sync.Once

	once.Do(func() {
		AccountNotificationUc = usecase.NewAccountNotificationUc(
			u.repo.AccountNotification(),
			u.repo.Account(),
		)
	})

	return AccountNotificationUc
}

func (u *usecaseRegistry) RtcCityGroup() usecase.RtcCityGroup {
	var RtcCityGroupUc usecase.RtcCityGroup
	var once sync.Once

	once.Do(func() {
		RtcCityGroupUc = usecase.NewRtcCityGroupUc(
			u.repo.RtcCityGroup(),
		)
	})

	return RtcCityGroupUc
}

func (u *usecaseRegistry) CodConfiguration() usecase.CodConfiguration {
	var codConfigurationUc usecase.CodConfiguration
	var once sync.Once

	once.Do(func() {
		codConfigurationUc = usecase.NewCodConfigurationUc(
			u.repo.CodConfiguration(),
			u.repo.PartnerLocation(),
			u.repo.Time(),
		)
	})

	return codConfigurationUc
}

func (u *usecaseRegistry) BalanceMinusPenalty() usecase.BalanceMinusPenalty {
	var balanceMinusPenaltyUnc usecase.BalanceMinusPenalty
	var once sync.Once

	once.Do(func() {
		balanceMinusPenaltyUnc = usecase.NewBalanceMinusPenaltyUc(
			u.repo.Account(),
			u.repo.BalanceMinusPenalty(),
			u.Permission(),
			u.repo.Time(),
		)
	})

	return balanceMinusPenaltyUnc
}

func (u *usecaseRegistry) DexBucketTiketConfiguration() usecase.DexBucketTiketConfiguration {
	var dexBucketTiketConfigurationUc usecase.DexBucketTiketConfiguration
	var once sync.Once

	once.Do(func() {
		dexBucketTiketConfigurationUc = usecase.NewDexBucketTiketConfigurationUc(
			u.repo.DexBucketTiketConfiguration(),
			u.repo.Time(),
		)
	})

	return dexBucketTiketConfigurationUc
}

func (u *usecaseRegistry) DexFakeDexConfiguration() usecase.DexFakeDexConfiguration {
	var dexFakeDexConfigurationUc usecase.DexFakeDexConfiguration
	var once sync.Once

	once.Do(func() {
		dexFakeDexConfigurationUc = usecase.NewDexFakeDexConfigurationUc(
			u.repo.DexFakeDexConfiguration(),
			u.repo.Time(),
		)
	})

	return dexFakeDexConfigurationUc
}

func (u *usecaseRegistry) DexBucketTicketAccount() usecase.DexBucketTicketAccount {
	var dexBucketTiketAccDexBucketTicketAccountUc usecase.DexBucketTicketAccount
	var once sync.Once

	once.Do(func() {
		dexBucketTiketAccDexBucketTicketAccountUc = usecase.NewDexBucketTicketAccountUc(
			u.repo.DexBucketTicketAccount(),
			u.repo.Time(),
		)
	})

	return dexBucketTiketAccDexBucketTicketAccountUc
}

func (u *usecaseRegistry) CsAccountLog() usecase.CsAccountLog {
	var csAccountLogUc usecase.CsAccountLog
	var once sync.Once

	once.Do(func() {
		csAccountLogUc = usecase.NewCsAccountLogUc(
			u.repo.CsAccountLog(),
			u.repo.Account(),
			u.repo.FlagManagementRepository(),
		)
	})

	return csAccountLogUc
}

func (u *usecaseRegistry) PickupSchedule() usecase.PickupSchedule {
	var pickupSchedule usecase.PickupSchedule
	var once sync.Once

	once.Do(func() {
		pickupSchedule = usecase.NewPickupSchedule(
			u.repo.Cache(),
			u.cfg,
			u.repo.PickupSchedule(),
			u.repo.City(),
			u.repo.Client(),
			u.repo.District(),
			u.repo.Pubsub(),
			u.repo.Account(),
			u.repo.PickupCorporateAddressRepository(),
			u.repo.PartnerLog(),
		)
	})

	return pickupSchedule
}

func (u *usecaseRegistry) InternationalDocumentConfiguration() usecase.InternationalDocumentConfiguration {
	var internationalDocumentConfigurationUc usecase.InternationalDocumentConfiguration
	var once sync.Once

	once.Do(func() {
		internationalDocumentConfigurationUc = usecase.NewInternationalDocumentConfigurationUc(
			u.repo.InternationalDocumentConfiguration(),
			u.repo.Account(),
			u.repo.FlagManagementRepository(),
			u.repo.Cache(),
			u.repo.City(),
			u.repo.Country(),
			u.repo.Partner(),
		)
	})

	return internationalDocumentConfigurationUc
}

func (u *usecaseRegistry) UserConsentConfiguration() usecase.UserConsentUsecase {
	var userConsentUc usecase.UserConsentUsecase
	var once sync.Once

	once.Do(func() {
		userConsentUc = usecase.NewUserConsentUseCase(
			usecase.SetUserConsentRepo(u.repo.ConfigurableUserConsent()),
		)
	})

	return userConsentUc
}

func (u *usecaseRegistry) BulkSTTDetail() usecase.BulkSTTer {
	var (
		bulkSTTDetailUcase usecase.BulkSTTer
		once               sync.Once
	)

	once.Do(func() {
		bulkSTTDetailUcase = usecase.NewBulkSTTDetail(
			u.cfg,
			usecase.PersistanceBulkSTTDetail{
				u.repo.BulkSttDetail(),
				u.repo.Bulk(),
				u.District(),
				u.Commodity(),
				u.repo.Client(),
				u.repo.District(),
				u.repo.BulkStt(),
			},
		)
	})

	return bulkSTTDetailUcase
}

func (u *usecaseRegistry) CargoConfigurationSearchFlight() usecase.CargoConfigurationSearchFlightUC {
	var cargoConfigurationSearchFlight usecase.CargoConfigurationSearchFlightUC
	var once sync.Once

	once.Do(func() {
		cargoConfigurationSearchFlight = usecase.NewCargoConfigurationSearchFlightUC(
			usecase.SetCargoConfigurationSearchFlightUCConfig(&u.cfg),
			usecase.SetCargoConfigurationSearchFlightUCRepo(u.repo.CargoConfigurationSearchFlight()),
			usecase.SetCargoConfigurationSearchFlightUCCityRepo(u.repo.City()),
			usecase.SetCargoConfigurationSearchFlightUCPartnerLogRepo(u.repo.PartnerLog()),
		)
	})

	return cargoConfigurationSearchFlight
}

func (u *usecaseRegistry) ConfigDfodPasti() usecase.ConfigDfodPasti {
	var configDfodPasti usecase.ConfigDfodPasti
	var once sync.Once

	once.Do(func() {
		configDfodPasti = usecase.NewConfigDfodPastiUc(
			u.repo.ConfigDfodPastiInactivePeriodRepository(),
			u.repo.ConfigDfodPastiExceptionRepository(),
			u.repo.Commodity(),
			u.repo.Time(),
		)
	})

	return configDfodPasti
}

func (u *usecaseRegistry) Otp() usecase.Otp {
	var otp usecase.Otp
	var once sync.Once

	once.Do(func() {
		otp = usecase.NewOtpUc(
			u.cfg,
			u.repo.OtpRepository(),
			u.repo.FlagManagementRepository(),
		)
	})

	return otp
}
