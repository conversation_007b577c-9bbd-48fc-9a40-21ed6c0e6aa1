package registry

import (
	"sync"

	"github.com/Lionparcel/horde/config/firebase"
	"github.com/Lionparcel/horde/config/recaptcha_enterprise"
	"github.com/Lionparcel/horde/registry/repository"
	"github.com/Lionparcel/horde/src/usecase"

	"github.com/Lionparcel/horde/config"
	configurable_cashback_ucase "github.com/Lionparcel/horde/src/modules/configurable_cashback/usecase"
	cftfUsecase "github.com/Lionparcel/horde/src/modules/configurable_top_up_fee/usecase"
	deviceauth "github.com/Lionparcel/horde/src/modules/deviceauth/usecase"
	tariffUC "github.com/Lionparcel/horde/src/modules/tariff/usecase"
)

// ModuleUsecaseRegistry ...
type ModuleUsecaseRegistry interface {
	ConfigurableTopUpFee() cftfUsecase.ConfigurableTopUpFee
	ConfigurableCasback() configurable_cashback_ucase.ConfigurableCashback
	TariffUc() tariffUC.Tariff
	TariffTokopedia() tariffUC.Tariff
	TariffBukalapak() tariffUC.Tariff
	DeviceAuth() deviceauth.DeviceAuthUsecase
}

type moduleUsecaseRegistry struct {
	repo    ModuleRepositoryRegistry
	oldRepo repository.RepositoryRegistry
	cfg     config.Config
}

// NewModuleUsecaseRegistry ...
func NewModuleUsecaseRegistry(cfg config.Config) (r ModuleUsecaseRegistry) {
	var moduleUcRegistry moduleUsecaseRegistry
	var once sync.Once

	once.Do(func() {
		repoReg := NewModuleRepoRegistry(cfg)
		oldRepoReg := repository.NewRepoRegistry(cfg)
		moduleUcRegistry = moduleUsecaseRegistry{repo: repoReg, cfg: cfg, oldRepo: oldRepoReg}
	})

	return &moduleUcRegistry
}

func (u *moduleUsecaseRegistry) ConfigurableTopUpFee() cftfUsecase.ConfigurableTopUpFee {
	var ucConfigurableTopUpFee cftfUsecase.ConfigurableTopUpFee
	var once sync.Once
	once.Do(func() {
		ucConfigurableTopUpFee = cftfUsecase.NewConfigurableTopUpFeeUc(
			&u.cfg,
			u.repo.ConfigurableTopUpFee(),
			u.repo.ConfigurablePartnerRepository(),
			u.oldRepo.FlagManagementRepository(),
			u.oldRepo.Time(),
			u.oldRepo.Account(),
		)
	})
	return ucConfigurableTopUpFee
}

func (u *moduleUsecaseRegistry) ConfigurableCasback() configurable_cashback_ucase.ConfigurableCashback {
	var configurableCasback configurable_cashback_ucase.ConfigurableCashback
	var once sync.Once

	once.Do(func() {
		configurableCasback = configurable_cashback_ucase.NewConfigurableCashback(
			&u.cfg,
			u.repo.ConfigurableCashbackRepository(),
			u.repo.ConfigurablePartnerRepository(),
			u.oldRepo.Account(),
		)
	})

	return configurableCasback
}

func (u *moduleUsecaseRegistry) TariffUc() tariffUC.Tariff {
	var uc tariffUC.Tariff
	var once sync.Once

	once.Do(func() {
		uc = tariffUC.NewTariffUc(
			&u.cfg,
			u.oldRepo.District(),
			u.oldRepo.Commodity(),
			u.oldRepo.Client(),
			u.oldRepo.PartnerLocation(),
			u.oldRepo.Time(),
			u.oldRepo.Cache(),
			u.oldRepo.Country(),
			u.oldRepo.ProductType(),
			u.oldRepo.FlagManagementRepository(),

			u.repo.TariffCityRateRepository(),
			u.repo.TariffClientCityRateRepository(),
			u.repo.TariffDistrictRateRepository(),
			u.repo.TariffClientDistrictRateRepository(),
			u.repo.TariffRateVersionRepository(),
			u.repo.TariffCommoditySurchargeRepository(),
			u.repo.TariffConfigurablePriceRepository(),
			u.repo.TariffPromoDiscountConfigurationRepository(),
			u.repo.TariffCodConfigurationRepository(),
			u.repo.TariffConfigurableWoodpackingCityRepository(),
			u.repo.TariffConfigurableRuleRepository(),
			u.repo.TariffEstimateSLARepository(),
			u.repo.TariffEmbargoRepository(),
			u.repo.SttPasRepository(),
		)
	})

	return uc
}

func (u *moduleUsecaseRegistry) TariffTokopedia() tariffUC.Tariff {
	var uc tariffUC.Tariff
	var once sync.Once

	once.Do(func() {
		uc = tariffUC.NewTariffUc(
			&u.cfg,
			u.oldRepo.District(),
			u.oldRepo.Commodity(),
			u.oldRepo.Client(),
			u.oldRepo.PartnerLocation(),
			u.oldRepo.Time(),
			u.oldRepo.Cache(),
			u.oldRepo.Country(),
			u.oldRepo.ProductType(),
			u.oldRepo.FlagManagementRepository(),

			u.repo.TariffCityRateRepository(),
			u.repo.TariffClientCityRateClone(), // tokopedia repo
			u.repo.TariffDistrictRateRepository(),
			u.repo.TariffClientDistrictRateCloneRepository(), // tokopedia repo
			u.repo.TariffRateVersionClone(),                  // tokopedia repo
			u.repo.TariffCommoditySurchargeRepository(),
			u.repo.TariffConfigurablePriceRepository(),
			u.repo.TariffPromoDiscountConfigurationRepository(),
			u.repo.TariffCodConfigurationRepository(),
			u.repo.TariffConfigurableWoodpackingCityRepository(),
			u.repo.TariffConfigurableRuleRepository(),
			u.repo.TariffEstimateSLARepository(),
			u.repo.TariffEmbargoRepository(),
			u.repo.SttPasRepository(),
		)
	})

	return uc
}

func (u *moduleUsecaseRegistry) TariffBukalapak() tariffUC.Tariff {
	var uc tariffUC.Tariff
	var once sync.Once

	once.Do(func() {
		uc = tariffUC.NewTariffUc(
			&u.cfg,
			u.oldRepo.District(),
			u.oldRepo.Commodity(),
			u.oldRepo.Client(),
			u.oldRepo.PartnerLocation(),
			u.oldRepo.Time(),
			u.oldRepo.Cache(),
			u.oldRepo.Country(),
			u.oldRepo.ProductType(),
			u.oldRepo.FlagManagementRepository(),

			u.repo.TariffCityRateRepository(),
			u.repo.TariffClientCityRateCloneBL(), // bukalapak repo
			u.repo.TariffDistrictRateRepository(),
			u.repo.TariffClientDistrictRateCloneBlRepository(), // bukalapak repo
			u.repo.TariffRateVersionCloneBL(),                  // bukalapak repo
			u.repo.TariffCommoditySurchargeRepository(),
			u.repo.TariffConfigurablePriceRepository(),
			u.repo.TariffPromoDiscountConfigurationRepository(),
			u.repo.TariffCodConfigurationRepository(),
			u.repo.TariffConfigurableWoodpackingCityRepository(),
			u.repo.TariffConfigurableRuleRepository(),
			u.repo.TariffEstimateSLARepository(),
			u.repo.TariffEmbargoRepository(),
			u.repo.SttPasRepository(),
		)
	})

	return uc
}

func (u *moduleUsecaseRegistry) DeviceAuth() deviceauth.DeviceAuthUsecase {
	var uc deviceauth.DeviceAuthUsecase
	var once sync.Once

	once.Do(func() {
		// Create token usecase instance
		tokenUc := usecase.NewTokenUc(u.cfg, u.oldRepo.AccountToken(), u.oldRepo.Account())

		uc = deviceauth.NewDeviceAuthUsecase(
			deviceauth.NewWebAuthUsecaseParams{
				Config:                 &u.cfg,
				AccountRepo:            u.oldRepo.Account(),
				DeviceRepo:             u.repo.DeviceRepository(),
				DeviceApprovalRepo:     u.repo.DeviceApprovalRepository(),
				DeviceAuthLogRepo:      u.repo.DeviceAuthLogRepository(),
				FirebaseRepo:           u.oldRepo.Firebase(firebase.NewGenesisMobileFcm()),
				TokenUc:                tokenUc,
				ActivityLogRepository:  u.oldRepo.ActivityLog(),
				CsAccountLogRepository: u.oldRepo.CsAccountLog(),
				PartnerRepository:      u.oldRepo.Partner(),
				ClientRepository:       u.oldRepo.Client(),
				CacheClient:            u.cfg.Cache(),
				PartnerLog:             u.oldRepo.PartnerLog(),
				RecaptchaEnterprise:    recaptcha_enterprise.New(),
			},
		)
	})

	return uc
}
