-- enhance table account
CREATE INDEX idx_username ON account (username);
CREATE INDEX idx_account_status ON account (account_status);
CREATE INDEX idx_account_type ON account (account_type);
CREATE INDEX idx_created_by ON account (created_by);
CREATE INDEX idx_updated_by ON account (updated_by);

-- enhance table account_role
CREATE INDEX account_role_type_idx ON account_role (account_role_type);

-- enhance table announcement
CREATE INDEX idx_pinned ON announcement (pinned);
CREATE INDEX idx_deleted_at ON announcement (deleted_at);
CREATE INDEX idx_name ON announcement (name);

-- enhance table booking_commission
CREATE INDEX idx_bc_code ON booking_commission (bc_code);

-- enhance table bulk
CREATE INDEX idx_created_by ON bulk (created_by);
CREATE INDEX idx_created_at ON bulk (created_at);

-- enhance table city
CREATE INDEX free_trade_zone_idx ON city (free_trade_zone);

-- enhance table client
CREATE INDEX idx_client_company_name ON client (client_company_name);
CREATE INDEX idx_client_parent_id ON client (client_parent_id);
CREATE INDEX idx_client_is_banned ON client (client_is_banned);
CREATE INDEX idx_client_district_code ON client (client_district_code);
CREATE INDEX idx_client_partner_pos_id ON client (client_partner_pos_id);
CREATE INDEX idx_client_code_elexys ON client (client_code_elexys);

-- enhance table commodity_surcharge
CREATE INDEX idx_commodity_surcharge_commodity_id ON commodity_surcharge (commodity_surcharge_commodity_id);
CREATE INDEX idx_commodity_surcharge_status ON commodity_surcharge (commodity_surcharge_status);
CREATE INDEX idx_commodity_surcharge_started_at ON commodity_surcharge (commodity_surcharge_started_at);
CREATE INDEX idx_commodity_surcharge_expired_at ON commodity_surcharge (commodity_surcharge_expired_at);

-- enhance table commodity
CREATE INDEX idx_commodity_is_active ON commodity (commodity_is_active);

-- enhance table commodity_group
CREATE INDEX idx_commodity_group_name ON commodity_group (commodity_group_name);

-- enhance table country
CREATE INDEX idx_name ON country (name);

-- enhance table customer
CREATE INDEX idx_customer_ref_id ON customer (customer_ref_id);
CREATE INDEX idx_customer_account_type ON customer (customer_account_type);

-- enhance table district
CREATE INDEX idx_status ON district (status);

-- enhance table driver
CREATE INDEX idx_driver_phone_number ON driver (driver_phone_number);

-- enhance table configurable_dtpol_product
CREATE INDEX idx_cdp_name ON configurable_dtpol_product (cdp_name);

-- enhance table hub
CREATE INDEX idx_hub_partner_id ON hub (hub_partner_id);

-- enhance table message_ym_wa_premium
CREATE INDEX idx_mywp_template_name ON message_ym_wa_premium (mywp_template_name);
CREATE INDEX idx_mywp_status ON message_ym_wa_premium (mywp_status);

-- enhance table message_configuration
CREATE INDEX idx_msg_conf_packet_type ON message_configuration (msg_conf_packet_type);
CREATE INDEX idx_msg_conf_booked_for ON message_configuration (msg_conf_booked_for);
CREATE INDEX idx_msg_conf_product ON message_configuration (msg_conf_product);
CREATE INDEX idx_msg_conf_origin_city ON message_configuration (msg_conf_origin_city);
CREATE INDEX idx_msg_conf_origin_city_exclude ON message_configuration (msg_conf_origin_city_exclude);
CREATE INDEX idx_msg_conf_pos_id ON message_configuration (msg_conf_pos_id);
CREATE INDEX idx_msg_conf_pos_id_exclude ON message_configuration (msg_conf_pos_id_exclude);
CREATE INDEX idx_msg_conf_client_id ON message_configuration (msg_conf_client_id);
CREATE INDEX idx_msg_conf_client_id_exclude ON message_configuration (msg_conf_client_id_exclude);

-- enhance table partner
CREATE INDEX idx_partner_name ON partner (partner_name);
CREATE INDEX idx_partner_external_code ON partner (partner_external_code);

-- enhance table stt_pas
CREATE INDEX idx_sp_product ON stt_pas (sp_product);
CREATE INDEX idx_sp_origin_3lc ON stt_pas (sp_origin_3lc);
CREATE INDEX idx_sp_destination_3lc ON stt_pas (sp_destination_3lc);

-- enhance table transport
CREATE INDEX idx_transport_type ON transport (transport_type);
CREATE INDEX idx_transport_code ON transport (transport_code);

-- enhance table vendor
CREATE INDEX vendor_name_idx ON vendor (vendor_name);
CREATE INDEX ref_id_idx ON vendor (ref_id);
CREATE INDEX tax_number_idx ON vendor (tax_number);
