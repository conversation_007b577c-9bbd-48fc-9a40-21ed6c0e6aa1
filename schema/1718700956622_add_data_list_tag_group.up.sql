INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('BKD (DFOD)', '{cod_amount},{consignee_name},{datetime},{delivery_sla},{shipper_name},{stt_number}', 'dfod', 'BKD', 'Hi {consignee_name},
 
Paket DFOD Anda senilai {cod_amount} dari pengirim {shipper_name} telah dibooking oleh Lion Parcel 
 
pada {datetime} dengan No. STT {stt_number}. Paket Anda akan sampai dengan estimasi pengiriman {delivery_sla}. 
Terimakasih telah menggunakan layanan Lion Parcel ! :)



Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps', '2021-07-14 14:39:20', 1, '2021-07-30 18:00:21', NULL);
INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('POD (DFOD)', '{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{recipient_name},{shipper_name},{stt_number},{proof_photo}', 'dfod', 'POD', 'Hai {consignee_name}! Paket DFOD dari {shipper_name} nomor {stt_number} sudah diterima oleh {recipient_name}! Silahkan cek bukti penerima berikut {proof_photo}. Terima kasih sudah menggunakan Lion Parcel! Ditunggu kiriman selanjutnya, ya!


Cobain aplikasi Lion Parcel, dijamin Berani Diandelin karena punya gratis Pick Up & Parcel Poin berkali-kali lipat! Yuk, download aplikasinya sekarang di https://bit.ly/AplikasiLP


Silahkan balas "YA" untuk mengaktifkan link!


------Pesan otomatis ini tidak terhubung dengan agen CS------', '2021-07-14 14:39:20', 1, '2024-01-02 19:32:58', NULL);
INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('DEX (DFOD)', '{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{rebuttal_link},{undeliver_reason},{paragraph_dex},{proof_photo}', 'dfod', 'DEX', 'Hai Pelanggan Lion Parcel. Paket DFOD nomor {stt_number} kamu tertunda karena {undeliver_reason} {proof_photo}. Jangan khawatir, kami akan kirim lagi di hari kerja berikutnya. Pastikan kamu dapat dihubungi ya.


{paragraph_dex}


Silahkan balas "YA" untuk mengaktifkan link!


------Pesan otomatis ini tidak terhubung dengan agen CS------', '2021-07-14 14:39:20', 1, '2024-01-02 19:32:58', NULL);
INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('DEL (DFOD)', '{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number}', 'dfod', 'DEL', 'Hi {consignee_name},

Paket DFOD Anda senilai {cod_amount} dari pengirim {shipper_name} dengan No. STT {stt_number} 

dan estimasi pengiriman {delivery_sla} sedang dalam pengantaran ke alamat Anda pada {datetime}. 

Kurir Lion Parcel yang mengantar paket Anda adalah {driver_name} dengan No. Telp 

{driver_phone_number}. Mohon siapkan biaya DFOD sesuai dengan paket pesanan Anda. Terimakasih 

telah menggunakan Lion Parcel ! :) 



Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps', '2021-07-14 14:39:20', 1, '2021-07-30 18:00:21', NULL);
INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('CODREJ (DFOD)', '{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{codrej_reason}', 'dfod', 'CODREJ', 'Hi {consignee_name},

Paket DFOD Anda senilai {cod_amount} dengan No. STT {stt_number} dan estimasi pengiriman 

{delivery_sla} telah kami kembalikan kepada pengirim {shipper_name} karena {codrej_reason} 

pada {datetime}. Kurir Lion Parcel yang mengantar paket Anda adalah {driver_name} dengan 

No. Telp {driver_phone_number}. Terimakasih telah menggunakan layanan Lion Parcel ! :) 



Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps', '2021-07-14 14:39:20', 1, '2021-07-30 18:00:21', NULL);
INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('HAL (DFOD)', '{consignee_name},{stt_number},{city_name},{shipper_name}', 'dfod', 'HAL', 'Hi {consignee_name}, paket kamu dengan nomor resi {stt_number} masih berada di fasilitas logistik kota {city_name}. Segera update data atau informasi pengiriman kamu ke https://wa.me/6281119600999 ya!

------Ini adalah pesan otomatis, mohon agar tidak membalas-------', '2021-07-14 14:39:20', 1, '2021-07-30 18:00:21', NULL);
INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('STI-DEST (DFOD)', '{consignee_name},{stt_number}', 'dfod', 'STI-DEST', 'Hi {consignee_name}, paket kamu dengan nomor resi {stt_number} telah sampai di kota tujuan! Kamu gak perlu khawatir mengenai paket kamu, karena akan segera dikirimkan ke alamat kamu!

Kamu juga bisa cek status paketmu, silakan klik link berikut:
https://lionparcel.com/track/stt?q={stt_number}

------Ini adalah pesan otomatis, mohon agar tidak membalas------', '2021-07-14 14:39:20', 1, '2021-07-30 18:00:21', NULL);
INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('CI (DFOD)', '{consignee_name},{shipper_name},{datetime},{delivery_sla},{stt_number}', 'dfod', 'CI', 'Hi {consignee_name}, Apakah kamu sudah menerima paket {stt_number} kamu? 
 Konfirmasi lebih lanjut hubungi Liona di https://wa.me/6281119600999 ya!
 ------Pesan otomatis ini tidak terhubung dengan agen CS------', '2023-11-09 21:11:41', 1, NULL, NULL);
INSERT INTO horde.list_tag_group
(list_tag_group_action, list_tag_group_tag, list_tag_group_type_cod, list_tag_group_status_stt, list_tag_group_content, list_tag_group_created_at, list_tag_group_created_by, list_tag_group_updated_at, list_tag_group_updated_by)
VALUES('CNX (DFOD)', '{consignee_name},{shipper_name},{datetime},{delivery_sla},{stt_number},{reason_cnx}', 'dfod', 'CNX', 'Hi {consignee_name}, pesanan kamu telah dibatalkan karena {reason_cnx}. 
 Informasi lebih lanjut hubungi Liona di https://wa.me/6281119600999 ya!
 ------Pesan otomatis ini tidak terhubung dengan agen CS------', '2023-11-09 21:11:41', 1, NULL, NULL);