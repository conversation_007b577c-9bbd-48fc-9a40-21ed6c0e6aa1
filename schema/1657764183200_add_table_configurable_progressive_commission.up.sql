CREATE TABLE `configurable_progressive_commission` (
  `cpc_id` int(11) NOT NULL AUTO_INCREMENT,
  `cpc_name` varchar(150) NULL,
  `cpc_start_date` timestamp NULL DEFAULT NULL,
  `cpc_end_date` timestamp NULL DEFAULT NULL,
  `cpc_applied_to` varchar(255) NOT NULL,
  `cpc_term_condition` TEXT NOT NULL,
  `cpc_created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cpc_created_by` int(11) NOT NULL,
  `cpc_updated_at` timestamp NULL DEFAULT NULL,
  `cpc_updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`cpc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
CREATE INDEX cpc_start_date_IDX USING BTREE ON configurable_progressive_commission (cpc_start_date);
CREATE INDEX cpc_end_date_IDX USING BTREE ON configurable_progressive_commission (cpc_end_date);
