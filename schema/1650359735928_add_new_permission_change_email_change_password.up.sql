INSERT INTO permission (name,created_at,parent_id)
	SELECT 'user_management_account_change_email', NOW(), (SELECT id FROM permission WHERE name = 'user_management_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'user_management_account_change_email');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'user_management_account_change_password', NOW(), (SELECT id FROM permission WHERE name = 'user_management_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'user_management_account_change_password');
