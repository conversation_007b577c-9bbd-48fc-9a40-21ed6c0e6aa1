CREATE TABLE IF NOT EXISTS `international_document_configuration` (
  `idc_id` int NOT NULL AUTO_INCREMENT,
  `idc_country_id` int NOT NULL,
  `idc_is_other_commodity` boolean NOT NULL DEFAULT FALSE,
  `idc_is_receiver_email` boolean NOT NULL DEFAULT FALSE,
  `idc_is_before_and_after_packing_image` boolean NOT NULL DEFAULT FALSE,
  `idc_is_ktp_image` boolean NOT NULL DEFAULT FALSE,
  `idc_is_npwp_image` boolean NOT NULL DEFAULT FALSE,
  `idc_status` varchar(20) NOT NULL,
  `idc_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `idc_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `idc_created_by` bigint DEFAULT NULL,
  `idc_updated_by` bigint DEFAULT NULL,
  PRIMARY KEY (`idc_id`),
  UNIQUE KEY `idx_unique_idc_country_id` (`idc_country_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
