-- BOOKING COMMISSION CONFIG

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'commission_config_booking_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'commission_config_booking_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'commission_config_booking_list_view', NOW(), (SELECT id FROM permission WHERE name = 'commission_config_booking_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'commission_config_booking_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'commission_config_booking_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'commission_config_booking_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'commission_config_booking_detail_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'commission_config_booking_detail_edit', NOW(), (SELECT id FROM permission WHERE name = 'commission_config_booking_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'commission_config_booking_detail_edit');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'commission_config_booking_add', NOW(), (SELECT id FROM permission WHERE name = 'commission_config_booking_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'commission_config_booking_add');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('commission_config_enable','commission_config_booking_enable','commission_config_booking_list_view','commission_config_booking_detail_view','commission_config_booking_detail_edit','commission_config_booking_add')
	AND ar.account_role_type IN ('internal');