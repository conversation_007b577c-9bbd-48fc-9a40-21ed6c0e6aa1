CREATE TABLE `dex_bucket_ticket_configuration` (
    `dbtc_id` varchar(36) NOT NULL,
    `dbtc_name` varchar(255) not null,
    `dbtc_description` varchar(255) null,
    `dbtc_status` varchar(20) not null,
    `dbtc_created_at` timestamp not null default current_timestamp,
    `dbtc_created_by` int(11) not null,
    `dbtc_updated_at` timestamp null default null on update current_timestamp,
    `dbtc_updated_by` int(11),
  PRIMARY KEY (`dbtc_id`),
  KEY `dbtc_status_IDX` (`dbtc_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
