CREATE TABLE IF NOT EXISTS cod_configuration (
	cco_id INT(11) NOT NULL AUTO_INCREMENT,
    cco_name varchar(255) NOT NULL DEFAULT '',
    cco_description LONGTEXT,
    cco_product_type LONGTEXT,
    cco_min_price INT(11) NOT NULL,
    cco_max_price INT(11) NOT NULL,
    cco_min_cod_value INT(11) NOT NULL,
    cco_cod_type LONGTEXT,
    cco_is_insurance Boolean NOT NULL DEFAULT false,
    cco_city_code LONGTEXT,
    cco_city_name LONGTEXT,
    cco_percentage_cod DOUBLE NOT NULL DEFAULT '0',
    cco_origin_commission_percentage DOUBLE NOT NULL DEFAULT '0',
    cco_destination_commission_percentage DOUBLE NOT NULL DEFAULT '0',
    cco_status ENUM('active', 'inactive') NOT NULL DEFAULT 'inactive',
    cco_created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    cco_created_by INT NOT NULL DEFAULT '0',
    cco_updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    cco_updated_by INT NOT NULL DEFAULT '0',

    PRIMARY KEY (`cco_id`),
    KEY idx_cco_id (cco_id),
    KEY idx_cco_name (cco_name),
    KEY idx_cco_status (cco_status)
);
