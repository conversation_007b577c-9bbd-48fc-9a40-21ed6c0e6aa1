-- LAST BALANCE
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'finance_last_balance_enable', NULL, NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'finance_last_balance_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'finance_last_balance_list_view_pos', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'finance_last_balance_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'finance_last_balance_list_view_pos');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'finance_last_balance_download', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'finance_last_balance_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'finance_last_balance_download');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'finance_last_balance_list_view_client', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'finance_last_balance_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'finance_last_balance_list_view_client');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'finance_last_balance_list_view_client_cod', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'finance_last_balance_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'finance_last_balance_list_view_client_cod');

-- BALANCE TRANSACTION
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'finance_balance_transaction_enable', NULL, NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'finance_balance_transaction_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'finance_balance_transaction_download', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'finance_balance_transaction_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'finance_balance_transaction_download');
