-- BULK BOOKING

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'bulk_booking_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_booking_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'bulk_booking_list_view', NOW(), (SELECT id FROM permission WHERE name = 'bulk_booking_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_booking_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'bulk_booking_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'bulk_booking_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_booking_detail_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'bulk_booking_create', NOW(), (SELECT id FROM permission WHERE name = 'bulk_booking_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_booking_create');


-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('bulk_booking_enable','bulk_booking_list_view','bulk_booking_detail_view','bulk_booking_create')
	AND ar.account_role_type IN ('pos','internal','client');


