CREATE TABLE user_consent (
    uc_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uc_ref_type VARCHAR(50) NOT NULL,
    uc_ref_id BIGINT UNSIGNED NOT NULL,
    uc_account_id BIGINT UNSIGNED NOT NULL,
    uc_type VARCHAR(50) NOT NULL,
    uc_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Membuat indeks pada kolom uc_ref_type
CREATE INDEX idx_uc_ref_type ON user_consent(uc_ref_type);

-- Membuat indeks pada kolom uc_ref_id
CREATE INDEX idx_uc_ref_id ON user_consent(uc_ref_id);

-- Membuat indeks pada kolom uc_account_id
CREATE INDEX idx_uc_account_id ON user_consent(uc_account_id);

-- Membuat indeks pada kolom uc_type
CREATE INDEX idx_uc_type ON user_consent(uc_type);

-- Membuat indeks pada kolom uc_type
CREATE INDEX idx_uc_created_at ON user_consent(uc_created_at);
