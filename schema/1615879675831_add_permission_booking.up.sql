INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'booking_detail_edit',
    NOW(),
    (SELECT id FROM permission WHERE name = 'booking_feature_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'booking_detail_edit');

-- booking detail edit
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "booking_detail_edit"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_console"));
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "booking_detail_edit"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_subconsole"));
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "booking_detail_edit"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_pos"));

-- booking detail view
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "booking_detail_view"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_console"));
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "booking_detail_view"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_subconsole"));

-- booking list view
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "booking_list_view"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_console"));
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "booking_list_view"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_subconsole"));