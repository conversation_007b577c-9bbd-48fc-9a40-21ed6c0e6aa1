INSERT INTO permission (name,created_at)
	SELECT 'update_del_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'update_del_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'update_del_list_view', NOW(), (SELECT id FROM permission WHERE name = 'update_del_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'update_del_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'update_del_create', NOW(), (SELECT id FROM permission WHERE name = 'update_del_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'update_del_create');


-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('update_del_enable','update_del_list_view','update_del_create')
	AND ar.account_role_type IN ('sub-console', 'console');