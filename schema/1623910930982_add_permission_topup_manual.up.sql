INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('topup_verification_enable','topup_verification_list_view','topup_verification_detail_view', 'topup_verification_verify')
	AND ar.account_role_type IN ('internal') AND ar.account_role_name IN ('admin_finance_verify');


INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('topup_verification_enable','topup_verification_list_view','topup_verification_detail_view', 'topup_verification_verify')
	AND ar.account_role_type IN ('internal') AND ar.account_role_name IN ('admin_finance');