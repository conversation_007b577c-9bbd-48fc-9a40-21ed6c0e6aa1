INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'progressive_commission_configuration_enable', '', NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'progressive_commission_configuration_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'progressive_commission_configuration_view_list', '', NOW(), (select id from permission where name = 'progressive_commission_configuration_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'progressive_commission_configuration_view_list');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'progressive_commission_configuration_view_detail', '', NOW(), (select id from permission where name = 'progressive_commission_configuration_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'progressive_commission_configuration_view_detail');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'progressive_commission_configuration_edit', '', NOW(), (select id from permission where name = 'progressive_commission_configuration_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'progressive_commission_configuration_edit');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'progressive_commission_configuration_create', '', NOW(), (select id from permission where name = 'progressive_commission_configuration_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'progressive_commission_configuration_create');
