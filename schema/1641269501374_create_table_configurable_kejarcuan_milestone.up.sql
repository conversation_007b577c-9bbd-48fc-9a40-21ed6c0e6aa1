CREATE TABLE `configurable_kejarcuan_milestone` (
  `ckm_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ckm_name` VARCHAR(200) NULL DEFAULT 0,
  `ckm_ck_id` INT NULL,
  `ckm_limit_income` FLOAT NOT NULL DEFAULT 0,
  `ckm_bonus_type` enum('item', 'cash') NOT NULL DEFAULT 'cash',
  `ckm_bonus_detail` TEXT NULL,
  `ckm_created_at` TIMESTAMP NULL,
  `ckm_created_by` INT NULL,
  `ckm_updated_at` TIMESTAMP NULL,
  `ckm_updated_by` INT NULL,
  PRIMARY KEY (`ckm_id`),
  KEY `idx_ckm_bonus_type` (`ckm_bonus_type`),
  KEY `idx_ckm_ck_id` (`ckm_ck_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
