
ALTER TABLE configurable_refund ADD CONSTRAINT refund_stt_last_status_UN UNIQUE KEY (refund_stt_last_status);

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,refund_quantifier, refund_amount_insurance, refund_origin_city_exclude, refund_penalty)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'STT ADJUSTED (AFTER PUP)', 70, 'base_rate,shipping_surcharge', 100, 'ftz,bth', 30)
ON DUPLICATE KEY UPDATE
    refund_configurable_price_id = (select configurable_price_id from configurable_price where configurable_price_type = 'refund'),
    refund_stt_last_status = 'STT ADJUSTED (AFTER PUP)',
    refund_amount = 70,
    refund_quantifier = 'base_rate,shipping_surcharge',
    refund_amount_insurance = 100,
    refund_origin_city_exclude = 'ftz,bth',
    refund_penalty = 30;        

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,refund_quantifier, refund_amount_insurance, refund_origin_city_exclude, refund_penalty)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'SHORTLAND', 70, 'base_rate,shipping_surcharge', 100, 'ftz,bth', 30)
ON DUPLICATE KEY UPDATE
    refund_configurable_price_id = (select configurable_price_id from configurable_price where configurable_price_type = 'refund'),
    refund_stt_last_status = 'SHORTLAND',
    refund_amount = 70,
    refund_quantifier = 'base_rate,shipping_surcharge',
    refund_amount_insurance = 100,
    refund_origin_city_exclude = 'ftz,bth',
    refund_penalty = 30;  

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount, refund_quantifier, refund_amount_insurance, refund_origin_city_exclude, refund_penalty)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'PICKUP_TRUCKING', 70, 'base_rate,shipping_surcharge', 100, 'ftz,bth', 30)
ON DUPLICATE KEY UPDATE
    refund_configurable_price_id = (select configurable_price_id from configurable_price where configurable_price_type = 'refund'),
    refund_stt_last_status = 'PICKUP_TRUCKING',
    refund_amount = 70,
    refund_quantifier = 'base_rate,shipping_surcharge',
    refund_amount_insurance = 100,
    refund_origin_city_exclude = 'ftz,bth',
    refund_penalty = 30; 

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount, refund_quantifier, refund_amount_insurance, refund_origin_city_exclude, refund_penalty)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'DROPOFF_TRUCKING', 70, 'base_rate,shipping_surcharge', 100, 'ftz,bth', 30)
ON DUPLICATE KEY UPDATE
    refund_configurable_price_id = (select configurable_price_id from configurable_price where configurable_price_type = 'refund'),
    refund_stt_last_status = 'DROPOFF_TRUCKING',
    refund_amount = 70,
    refund_quantifier = 'base_rate,shipping_surcharge',
    refund_amount_insurance = 100,
    refund_origin_city_exclude = 'ftz,bth',
    refund_penalty = 30; 

