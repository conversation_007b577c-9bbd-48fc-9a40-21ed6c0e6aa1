INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'console_stt_payment_status_enable', '', NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'console_stt_payment_status_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'console_stt_payment_status_view', '', NOW(), (select id from permission where name = 'komisi_progressive_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'console_stt_payment_status_view');