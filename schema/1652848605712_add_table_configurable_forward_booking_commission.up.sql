-- CREATE TABLE
CREATE TABLE configurable_forward_booking_commission (
 cfbc_id INT(20) auto_increment NOT NULL PRIMARY KEY,
 cfbc_configurable_price_id INT unsigned,
 cfbc_commission_type VARCHAR (100) NULL,
 cfbc_quantifier VARCHAR(255) NULL,
 cfbc_commission_amount DOUBLE DEFAULT 0 NULL,
 cfbc_applied_to VARCHAR(255) NULL,
 cfbc_shipment_prefix VARCHAR(255) NULL,
 cfbc_commission_reference VARCHAR(255) NULL
)
ENGINE=InnoDB
DEFAULT CHARSET=latin1
COLLATE=latin1_swedish_ci;
CREATE INDEX cfbc_configurable_price_id_IDX USING BTREE ON configurable_forward_booking_commission (cfbc_configurable_price_id);

-- ALTER TABLE
ALTER TABLE configurable_price MODIFY COLUMN
configurable_price_type enum (
'heavy_weight_surcharge','insurance','woodpacking','stt_adjustment','refund','forward_booking_commission'
) NOT NULL DEFAULT 'heavy_weight_surcharge';

-- INSERT DATA TO TABLE
INSERT INTO configurable_price (configurable_price_id, configurable_price_name, configurable_price_type, configurable_price_description, configurable_price_status, configurable_price_created_at, configurable_price_created_by, configurable_price_updated_at, configurable_price_updated_by) 
VALUES (6, 'FORWARD BOOKING COMMISSION', 'forward_booking_commission', 'Untuk Perhitungan Forward Rate Commission', 'active', NOW(), 1, NOW(), 1);
INSERT INTO configurable_forward_booking_commission (cfbc_id,cfbc_configurable_price_id,cfbc_commission_type,cfbc_quantifier,cfbc_commission_amount,cfbc_applied_to,cfbc_shipment_prefix,cfbc_commission_reference) 
VALUES (1,6,"percentage","origin_forward_rate,destination_forward_rate",0,"shipment,retail,client","AP,AS","total_harga_forward_rate");