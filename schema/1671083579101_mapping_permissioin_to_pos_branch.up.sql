-- NOTIFICATION PERMISSION

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
    SELECT 'notification_enable', '', NOW(), 0
    WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'notification_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
    SELECT 'notification_view_list', '', NOW(), (SELECT `id` FROM `permission` WHERE `name` = 'notification_enable')
    WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'notification_view_list');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
    SELECT 'notification_view_detail', '', NOW(), (SELECT `id` FROM `permission` WHERE `name` = 'notification_enable')
    WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'notification_view_detail');