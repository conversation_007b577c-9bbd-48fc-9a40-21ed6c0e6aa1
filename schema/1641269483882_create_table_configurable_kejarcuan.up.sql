CREATE TABLE `configurable_kejarcuan` (
  `ck_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ck_name` VARCHAR(200) NULL DEFAULT 0,
  `ck_min_income` FLOAT NOT NULL DEFAULT 0,
  `ck_max_income` FLOAT NOT NULL DEFAULT 0,
  `ck_type` enum('All Pos') NOT NULL DEFAULT 'All Pos',
  `ck_valid_date_start` TIMESTAMP NULL,
  `ck_valid_date_end` TIMESTAMP NULL,
  `ck_status` enum('active','waiting', 'expired') NOT NULL DEFAULT 'waiting',
  `ck_toc` TEXT NULL,
  `ck_created_at` TIMESTAMP NULL,
  `ck_created_by` INT NULL,
  `ck_updated_at` TIMESTAMP NULL,
  `ck_updated_by` INT NULL,
  PRIMARY KEY (`ck_id`),
  KEY `idx_ck_type` (`ck_type`),
  KEY `idx_ck_valid_date_start` (`ck_valid_date_start`),
  KEY `idx_ck_valid_date_end` (`ck_valid_date_end`),
  KEY `idx_ck_status` (`ck_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
