INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('service_delay_network_enable', NULL, NOW(), 0),
       ('service_delay_network_list_view', NULL, NOW(), 0),
       ('service_delay_network_detail_view', NULL, NOW(), 0),
       ('service_delay_network_create', NULL, NOW(), 0),
       ('service_delay_network_detail_edit', NULL, NOW(), 0);

INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "service_delay_network_enable" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Network" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_list_view" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Network" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_detail_view" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Network" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_create" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Network" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_detail_edit" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Network" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_enable" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Helpdesk" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_list_view" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Helpdesk" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_detail_view" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Helpdesk" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_create" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Helpdesk" LIMIT 1)),
       ((SELECT id FROM permission WHERE `name` = "service_delay_network_detail_edit" LIMIT 1), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "Helpdesk" LIMIT 1));