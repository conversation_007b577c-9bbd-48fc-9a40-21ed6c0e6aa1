-- Alter driver
ALTER TABLE `driver`
ADD COLUMN `driver_external_id` BIGINT NULL AFTER `driver_partner_id`,
ADD COLUMN `driver_hub_id` BIGINT NULL AFTER `driver_external_id`,
ADD COLUMN `driver_shuttle_id` BIGINT NULL AFTER `driver_hub_id`,
ADD INDEX `idx_driver_external_id` (`driver_external_id`),
ADD INDEX `idx_driver_hub_id` (`driver_hub_id`),
ADD INDEX `idx_driver_shuttle_id` (`driver_shuttle_id`);

-- Table shuttle
CREATE TABLE `shuttle` (
  `shuttle_id` BIGINT NOT NULL,
  `shuttle_code` VARCHAR(100) NOT NULL,
  `shuttle_name` VARCHAR(300) NOT NULL,
  `shuttle_address` VARCHAR(1000) NOT NULL,
  `shuttle_map_url` VARCHAR(1000) NOT NULL,
  `shuttle_region` VARCHAR(1000) NOT NULL,
  `latitude` DOUBLE NOT NULL,
  `longitude` DOUBLE NOT NULL,
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`shuttle_id`),
  INDEX `idx_shuttle_code` (`shuttle_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table driver_coverage
CREATE TABLE `driver_coverage` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `driver_id` BIGINT NOT NULL,
  `driver_district_id` BIGINT NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_driver_coverage_driver_idx` (`driver_id`),
  KEY `fk_driver_coverage_district_idx` (`driver_district_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Table account_driver
CREATE TABLE `account_driver` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `account_id` BIGINT NOT NULL,
  `driver_id` BIGINT NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_account_driver_account_idx` (`account_id`),
  KEY `fk_account_driver_driver_idx` (`driver_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;