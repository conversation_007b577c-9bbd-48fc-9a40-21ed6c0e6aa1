CREATE TABLE configurable_stt_adjustment (
	stt_adjustment_id INT auto_increment NOT NULL,
	stt_adjustment_configurable_price_id INT NOT NULL,
	stt_adjustment_stt_adjusted_by varchar(100) NOT NULL,
	stt_adjustment_penalty_apply_for varchar(100) NOT NULL,
  	stt_adjustment_updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  	stt_adjustment_updated_by INT NOT NULL,
	CONSTRAINT configurable_stt_adjustment_PK PRIMARY KEY (stt_adjustment_id)
)
ENGINE=InnoDB
DEFAULT CHARSET=latin1
COLLATE=latin1_swedish_ci;
