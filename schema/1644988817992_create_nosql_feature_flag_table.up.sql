CREATE TABLE `nosql_feature_flag` (
  `nff_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `nff_key_name` varchar(150) DEFAULT NULL,
  `nff_key_value` int(1) DEFAULT 0,
  PRIMARY KEY (`nff_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `nosql_feature_flag` (`nff_key_name`, `nff_key_value`) VALUES ('v2customer', 0);
INSERT INTO `nosql_feature_flag` (`nff_key_name`, `nff_key_value`) VALUES ('v2commodity', 0);
INSERT INTO `nosql_feature_flag` (`nff_key_name`, `nff_key_value`) VALUES ('v2announcement', 0);
