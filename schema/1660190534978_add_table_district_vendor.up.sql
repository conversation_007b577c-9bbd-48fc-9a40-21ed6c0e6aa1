CREATE TABLE `district_vendor` (
  `dv_id` int(11) NOT NULL AUTO_INCREMENT,
  `dv_district_id` int(11) NULL,
  `dv_state` varchar(255) NULL DEFAULT NULL,
  `dv_province` varchar(255) NULL DEFAULT NULL,
  `dv_city` varchar(255) NOT NULL,
  `dv_partner_code` varchar(255) NOT NULL,
  `dv_customs_clearance_incoterm` varchar(255) NOT NULL,
  `dv_customs_clearance_port_code` varchar(255) NOT NULL,
  `dv_customs_clearance_partner_code` varchar(255) NOT NULL,
  `dv_created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `dv_created_by` int(11) DEFAULT 0,
  `dv_created_name` varchar(255) DEFAULT "",
  `dv_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `dv_updated_by` int(11) DEFAULT 0,
  `dv_updated_name` varchar(255) DEFAULT "",
  PRIMARY KEY (`dv_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
CREATE INDEX dv_district_id_IDX USING BTREE ON district_vendor (dv_district_id);
