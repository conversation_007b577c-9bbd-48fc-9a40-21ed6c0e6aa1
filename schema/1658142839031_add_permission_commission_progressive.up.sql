INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'komisi_progressive_enable', '', NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'komisi_progressive_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'komisi_progressive_edit', '', NOW(), (select id from permission where name = 'komisi_progressive_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'komisi_progressive_edit');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'komisi_progressive_view', '', NOW(), (select id from permission where name = 'komisi_progressive_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'komisi_progressive_view');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'komisi_progressive_list', '', NOW(), (select id from permission where name = 'komisi_progressive_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'komisi_progressive_list');
