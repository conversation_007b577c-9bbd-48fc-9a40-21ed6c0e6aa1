CREATE TABLE IF NOT EXISTS `service_delay` (
    `sd_id` int NOT NULL AUTO_INCREMENT,
    `sd_name` varchar(100) NOT NULL DEFAULT '',
    `sd_remarks` varchar(255) NOT NULL DEFAULT '',
    `sd_city_id` int NOT NULL,
    `sd_start_date` datetime NOT NULL,
    `sd_end_date` datetime NULL,
    `sd_created_by` int NOT NULL DEFAULT 0,
    `sd_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `sd_updated_by` int NULL DEFAULT NULL,
    `sd_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`sd_id`),
    INDEX `idx_sd_name` (`sd_name` ASC),
    INDEX `idx_sd_start_date` (`sd_start_date` ASC),
    INDEX `idx_sd_end_date` (`sd_end_date` ASC)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `service_delay_district` (
    `sdd_id` int NOT NULL AUTO_INCREMENT,
    `sdd_sd_id` int NOT NULL,
    `sdd_district_id` int NOT NULL DEFAULT 0,
    `sdd_is_delete` boolean NOT NULL DEFAULT 0,
    `sdd_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `sdd_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`sdd_id`),
    INDEX `idx_sdd_sd_id` (`sdd_sd_id` ASC),
    INDEX `idx_sdd_district_id` (`sdd_district_id` ASC),
    INDEX `idx_sdd_is_delete` (`sdd_is_delete` ASC)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8;
