INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('pickup_manifest_enable', 'to show pickup manifest menu on sidebar', NOW(), 0);
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('pickup_manifest_list_view', 'to be able to display pickup manifest list', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'pickup_manifest_enable') );
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('pickup_manifest_create', 'to show button create pickup manifest to be able to create new pickup manifest', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'pickup_manifest_enable') );

INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_sc_enable', 'to show STI-SC menu on sidebar', NOW(), 0);
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_sc_list_view', 'to be able to display STI-SC list', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'sti_sc_enable'));
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_sc_create', 'to show button create STI-SC to be able to create new STI-SC', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'sti_sc_enable'));

INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_enable', 'to show STI menu on sidebar', NOW(), 0);
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_list_view', 'to be able to display STI list', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'sti_enable'));
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_create', 'to show button create STI to be able to create new STI', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'sti_enable'));

INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('bagging_enable', 'to show bagging menu on sidebar', NOW(), 0);
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('bagging_list_view', 'to be able to display bagging list', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'bagging_enable'));
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('bagging_create', 'to show button create bagging to be able to create new bagging', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'bagging_enable'));
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('bagging_detail_view', 'to view detail bagging', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'bagging_enable'));
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) 
VALUES ('bagging_detail_edit', 'to show button edit bagging to be able to edit detail bagging', NOW(), (SELECT p.id FROM permission p WHERE p.name = 'bagging_enable'));


INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'pickup_manifest_enable'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'pickup_manifest_list_view'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'pickup_manifest_create'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'sti_sc_enable'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'sti_sc_list_view'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'sti_sc_create'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'sti_enable'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'sti_list_view'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'sti_create'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_enable'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_list_view'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_create'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_detail_view'), 4);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_detail_edit'), 1);
