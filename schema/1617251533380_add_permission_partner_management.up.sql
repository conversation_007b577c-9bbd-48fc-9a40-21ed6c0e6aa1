-- permission partner management enable
INSERT INTO permission (name, created_at)
  SELECT
    'partner_management_enable',
    NOW()
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'partner_management_enable');

-- permission partner management view list
INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'partner_management_view_list',
    NOW(),
    (SELECT id FROM permission WHERE name = 'partner_management_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'partner_management_view_list');

-- permission partner management create
INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'partner_management_create',
    NOW(),
    (SELECT id FROM permission WHERE name = 'partner_management_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'partner_management_create');

-- permission partner management view detail
INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'partner_management_view_detail',
    NOW(),
    (SELECT id FROM permission WHERE name = 'partner_management_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'partner_management_view_detail');

-- permission partner management edit
INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'partner_management_edit',
    NOW(),
    (SELECT id FROM permission WHERE name = 'partner_management_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'partner_management_edit');



-- partner management enable
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "partner_management_enable"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_internal"));

-- partner management view list
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "partner_management_view_list"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_internal"));

-- partner management create
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "partner_management_create"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_internal"));

-- partner management view detail
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "partner_management_view_detail"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_internal"));

-- partner management edit
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "partner_management_edit"), (SELECT account_role_id FROM account_role WHERE `account_role_name` = "admin_internal"));