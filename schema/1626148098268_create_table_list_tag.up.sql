CREATE TABLE `list_tag` (
  `list_tag_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `list_tag_text` varchar(250) NOT NULL DEFAULT '',
  `list_tag_group` varchar(250) NOT NULL DEFAULT '',
  `list_tag_table_name` varchar(250) NOT NULL DEFAULT '',
  `list_tag_field_name` varchar(250) NOT NULL DEFAULT '',
  `list_tag_description` varchar(250) NOT NULL DEFAULT '',
  `list_tag_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `list_tag_created_by` int(11) unsigned DEFAULT NULL,
  `list_tag_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `list_tag_updated_by` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`list_tag_id`),
  KEY `idx_list_tag_text` (`list_tag_text`),
  KEY `idx_list_tag_group` (`list_tag_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;