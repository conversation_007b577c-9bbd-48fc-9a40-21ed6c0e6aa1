
-- INSERT sti dest permission --
-- START INSERT --
INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'sti_dest_enable',
             null,
             NOW(),
             0) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'sti_dest_enable');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'sti_dest_list_view',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'sti_dest_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'sti_dest_list_view');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'sti_dest_create',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'sti_dest_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'sti_dest_create');
-- END INSERT --

-- DELETE sti dest permission --
-- START INSERT--
DELETE
FROM role_permission
WHERE permission_id IN
      (SELECT id
       FROM permission
       WHERE name IN (
           'sti_dest_enable',
           'sti_dest_list_view',
           'sti_dest_create'
           )
         AND account_role_id in (SELECT account_role_id FROM account_role WHERE account_role_type in ('console')));
-- END DELETE--

-- INSERT new role bulk permission --
-- START INSERT--
INSERT INTO role_permission (permission_id, account_role_id)
SELECT p.id, ar.account_role_id
FROM permission p
         CROSS JOIN account_role ar
WHERE p.name IN (
           'sti_dest_enable',
           'sti_dest_list_view',
           'sti_dest_create'
           )
  AND ar.account_role_type IN
      ('console');
-- END INSERT--
