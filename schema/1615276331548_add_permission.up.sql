INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('shortland_enable', NULL, NOW(), 0);
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('shortland_list_view', NULL, NOW(), (SELECT p.id FROM permission p WHERE p.`name` = "shortland_enable"));

INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "shortland_enable"), 2);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "shortland_list_view"), 2);

INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "shortland_enable"), 3);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "shortland_list_view"), 3);
