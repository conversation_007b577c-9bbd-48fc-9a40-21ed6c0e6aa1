 -- PUP

 -- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'pup_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'pup_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'pup_create', NOW(), (SELECT id FROM permission WHERE name = 'pup_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'pup_create');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'pup_list_view', NOW(), (SELECT id FROM permission WHERE name = 'pup_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'pup_list_view');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('pup_enable','pup_create','pup_list_view')
	AND ar.account_role_type IN ('internal','client');