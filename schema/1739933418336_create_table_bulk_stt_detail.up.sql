CREATE TABLE IF NOT EXISTS bulk_stt_detail (
    bsd_id BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
    bsd_bulk_stt_id BIGINT NOT NULL,
    bsd_row_num INT UNSIGNED NOT NULL,
    bsd_request TEXT NOT NULL,
    bsd_error TEXT NOT NULL,
    bsd_status VARCHAR(255) NOT NULL,
    bsd_stt_no VARCHAR(20) NOT NULL,
    bsd_stt_total_amount DOUBLE UNSIGNED DEFAULT 0,
    bsd_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    bsd_updated_at TIMESTAMP NULL DEFAULT NULL,
    INDEX idx_bsd_bulk_stt_id (bsd_bulk_stt_id),
    INDEX idx_bsd_bulk_stt_id_status (bsd_bulk_stt_id, bsd_status)
);