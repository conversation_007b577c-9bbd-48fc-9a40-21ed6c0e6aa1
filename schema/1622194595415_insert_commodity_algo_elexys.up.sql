-- KEMEJA
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'GAR 5', 'KEMEJA', '0', 'KEMEJA', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, NULL, '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'GAR 5');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'GAR 5';


-- OBAT
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'OBAT', 'OBAT', '0', 'OBAT', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'OBAT');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'OBAT';

-- DOKUMEN
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'DOC', 'DOKUMEN / DOCUMENT', '0', 'DOKUMEN / DOCUMENT', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'DOC');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'DOC';

-- MAKANAN KERING
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'FOD', 'MAKANAN KERING / DRY FOOD', '0', 'MAKANAN KERING / DRY FOOD', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'FOD');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'FOD';

-- PERLENGKAPAN RUMAH TANGGA
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'PRTG', 'PERLENGKAPAN RUMAH TANGGA / HOME APPLIANCES', '0', 'PERLENGKAPAN RUMAH TANGGA / HOME APPLIANCES', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'PRTG');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'PRTG';

-- MAKANAN BEKU
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'PER4', 'MAKANAN BEKU / FROZEN FOOD', '0', 'MAKANAN BEKU / FROZEN FOOD', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'PER4');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'PER4';

-- ACCESSORIES
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'ACC', 'ACCESSORIES - AKSESORIS', '0', 'ACCESSORIES - AKSESORIS', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'ACC');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'ACC';

-- elektronik
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'EOT 5', 'elektronik - Barang elektronik lainnya', '0', 'elektronik - Barang elektronik lainnya', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'EOT 5');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'EOT 5';

-- BAN
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'BAN', 'BAN', '0', 'BAN', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'BAN');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'BAN';

-- SEKRUP
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'SRPB', 'SEKRUP, BAUT DAN MUR', '0', 'SEKRUP, BAUT DAN MUR', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'SRPB');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'SRPB';

-- BIBIT TANAMAN
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'PEF2', 'BIBIT TANAMAN / SEED PLAN', '0', 'BIBIT TANAMAN / SEED PLAN', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'PEF2');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'PEF2';

-- IKAN BEKU
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'PES', 'IKAN BEKU / FROZEN FISH', '0', 'IKAN BEKU / FROZEN FISH', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'PES');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'PES';

-- FRUITS
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'PEP5', 'FRUITS / BUAH ( BUAH – BUAHAN )', '0', 'FRUITS / BUAH ( BUAH – BUAHAN )', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'PEP5');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'PEP5';

-- Makanan dan minuman
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'MKN 1', 'Makanan dan minuman - air mineral', '0', 'Makanan dan minuman - air mineral', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'MKN 1');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'MKN 1';

-- KEJU
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'KEJU', 'KEJU', '0', 'KEJU-KEJU', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'KEJU');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'KEJU';

-- KEPITING HIDUP
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'AVI12', 'KEPITING HIDUP / LIVE CRAB', '0', 'GAR 5-KEMEJA', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'AVI12');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'AVI12';

-- Bunga Hidup
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'DG2A', 'Bunga Hidup (Potong)', '0', 'Bunga Hidup (Potong)', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'DG2A');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'DG2A';

-- CUTTING FLOWER / BUNGA POTONG
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'PEF4', 'CUTTING FLOWER / BUNGA POTONG', '0', 'CUTTING FLOWER / BUNGA POTONG', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'PEF4');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'PEF4';

-- BINATANG HIDUP / LIVE ANIMAL
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'ANI', 'BINATANG HIDUP / LIVE ANIMAL', '0', 'BINATANG HIDUP / LIVE ANIMAL', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'ANI');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'ANI';

-- Buah Durian
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'PEP7', 'Buah Durian', '0', 'Buah Durian', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'PEP7');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'PEP7';

-- Banknotes
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'BNK', 'Banknotes', '0', 'Banknotes', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'BNK');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'BNK';

-- GENERAL OTHERS
INSERT INTO `commodity` 
    (`commodity_code`, `commodity_name`, `commodity_group_id`, `commodity_description`, `created_at`, `updated_at`, `created_by`, `updated_by`, `commodity_is_active`, `commodity_surcharge_applicable`, `commodity_document_surcharge`, `commodity_hs`, `commodity_service_type`, `commodity_min_price`) 
    SELECT 'GEN', 'GENERAL OTHERS - GENERAL LAINNYA', '0', 'GENERAL OTHERS - GENERAL LAINNYA', CURRENT_TIMESTAMP, NULL, '0', '0', '1', '0', '0', NULL, 'package', '0'
    WHERE NOT EXISTS (SELECT commodity_code FROM commodity WHERE commodity_code = 'GEN');

-- make sure commodity is active
UPDATE commodity SET commodity_is_active = TRUE WHERE commodity_code = 'GEN';

