CREATE TABLE IF NOT EXISTS `rtc_city_group` (
    `rcg_id` int NOT NULL AUTO_INCREMENT,
    `rcg_city_code` varchar(100) NOT NULL DEFAULT '',
    `rcg_group_code` varchar(100) NOT NULL DEFAULT '',
    `rcg_product_type` varchar(100) NOT NULL DEFAULT 'REGPACK',
    `rcg_status` varchar(100) NOT NULL DEFAULT '',
    `rcg_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `rcg_created_by` int NOT NULL DEFAULT 0,
    `rcg_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `rcg_updated_by` int NULL DEFAULT NULL,
    PRIMARY KEY (`rcg_id`),
    INDEX `idx_rcg_city_code` (`rcg_city_code`),
    INDEX `idx_rcg_rcg_group_code` (`rcg_group_code`),
    INDEX `idx_rcg_product_type` (`rcg_product_type`)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8;