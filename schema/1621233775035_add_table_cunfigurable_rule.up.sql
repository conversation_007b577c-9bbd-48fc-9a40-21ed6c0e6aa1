CREATE TABLE IF NOT EXISTS `configurable_rule` (
  `cr_id` INT NOT NULL AUTO_INCREMENT,
  `cr_product_type` VARCHAR(150) NULL,
  `cr_flag` ENUM('campaign', 'default') NOT NULL DEFAULT 'default',
  `cr_rules` VARCHAR(45) NULL,
  `cr_excluded_booked_for` TEXT NULL,
  `cr_created_by` INT NULL,
  `cr_created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `cr_updated_at` TIMESTAMP NULL,
  PRIMARY KEY (`cr_id`),
  INDEX `idx_cr_excluded_booked_for` (`cr_excluded_booked_for`(500) ASC),
  INDEX `idx_cr_flag` (`cr_flag` ASC),
  INDEX `idx_cr_rules` (`cr_rules` ASC),
  INDEX `idx_cr_product_type` (`cr_product_type` ASC))
ENGINE = InnoDB;

INSERT INTO `configurable_rule` (`cr_id`, `cr_product_type`, `cr_flag`, `cr_rules`, `cr_excluded_booked_for`, `cr_created_by`, `cr_created_at`, `cr_updated_at`)
VALUES
	(1, 'REGPACK', 'campaign', 'sikat-rules-0.5', '{\"pos\":false,\"client\":[357,358]}', 1, '2021-05-17 12:39:45', NULL),
	(2, 'ONEPACK', 'campaign', 'sikat-rules-0.5', '{\"pos\":false,\"client\":[357,358]}', 1, '2021-05-17 12:39:45', NULL),
	(3, 'REGPACK', 'default', 'rules-0.31', '{\"pos\":false,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL),
	(4, 'ONEPACK', 'default', 'rules-0.31', '{\"pos\":false,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL),
	(5, 'MIXPACK', 'default', 'rules-0.31', '{\"pos\":false,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL),
	(6, 'DORPACK', 'default', '-', '{\"pos\":true,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL),
	(7, 'SDPACK', 'default', 'rules-0.31', '{\"pos\":false,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL),
	(8, 'DOCUPACK', 'default', 'rules-0.31', '{\"pos\":false,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL),
	(9, 'LANDPACK', 'default', 'rules-0.01', '{\"pos\":false,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL),
	(10, 'BIGPACK', 'default', 'rules-0.01', '{\"pos\":false,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL),
	(11, 'INTERPACK', 'default', 'rules-0.01', '{\"pos\":false,\"client\":[0]}', 1, '2021-05-17 12:38:45', NULL);
