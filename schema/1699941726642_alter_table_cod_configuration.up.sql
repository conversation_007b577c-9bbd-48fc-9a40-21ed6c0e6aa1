ALTER TABLE `cod_configuration`
ADD `cco_is_dfod` tinyint(1) NOT NULL default 0 AFTER `cco_shipment_prefix`,
ADD `cco_origin_min_commission` int(11) NOT NULL DEFAULT 0 AFTER `cco_is_dfod`,
ADD `cco_destination_min_commission` int(11) NOT NULL DEFAULT 0 AFTER `cco_origin_min_commission`,
ADD INDEX `idx_cco_is_dfod` (`cco_is_dfod`);

INSERT INTO `cod_configuration`
(cco_name, cco_type, cco_description, cco_product_type, cco_min_price, cco_max_price, cco_min_cod_value, cco_cod_type, cco_is_insurance, cco_city_code, cco_city_name, cco_percentage_cod, cco_origin_commission_percentage, cco_destination_commission_percentage, cco_status, cco_shipment_prefix, cco_is_dfod, cco_origin_min_commission, cco_destination_min_commission, cco_created_at, cco_created_by, cco_updated_at, cco_updated_by)
VALUES('DFOD Retail', 'dfod_retail', 'Konfigurasi COD Ongkir atau Delivery Fee on Delivery untuk Retail', 'REGPACK', 0, 1000000000, 1000, 'goods_price', 0, 'ALL', 'ALL', 4, 1.5, 1, 'active', '', 1, 375, 250, CURRENT_TIMESTAMP, 0, CURRENT_TIMESTAMP, 0);
