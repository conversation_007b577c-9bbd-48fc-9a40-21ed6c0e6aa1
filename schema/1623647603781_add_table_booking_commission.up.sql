create table booking_commission
(
    bc_id int auto_increment,
    bc_bulk_id int default 0 not null,
    bc_tier_base varchar(50) default '' not null,
    bc_tier_price_reference varchar(50) default '' not null,
    bc_commission_reference varchar(50) default '' not null,
    bc_commission_type varchar(50) default '' not null,
    bc_commission_quantifier varchar(50) default '' not null,
    bc_booking_source varchar(50) default '' not null,
    bc_applied_to varchar(50) default '' not null,
    bc_shipment_prefix varchar(20) default '' not null,
    bc_created_id int default 0 not null,
    bc_created_at timestamp default now() not null,
    bc_updated_id int default 0 not null,
    bc_updated_at timestamp null,
    constraint booking_commission_pk
        primary key (bc_id)
);

create index bc_shipment_prefix_index
	on booking_commission (bc_shipment_prefix);

create index idx_bc_applied_to
	on booking_commission (bc_applied_to);

create index idx_bc_booking_source
	on booking_commission (bc_booking_source);

create index idx_bc_bulk_id
	on booking_commission (bc_bulk_id);


create table booking_commission_detail
(
	bc_detail_id int auto_increment,
	bc_detail_bc_id int default 0 not null,
	bc_detail_origin_city_id varchar(50) default '' not null,
	bc_detail_destination_city_id varchar(50) default '' not null,
	bc_detail_product_name varchar(50) default '' null,
	bc_detail_commodity_code varchar(50) default '' null,
	constraint booking_commission_detail_pk
		primary key (bc_detail_id)
);

create index idx_bc_detail_origin_city_id
	on booking_commission_detail (bc_detail_origin_city_id);

create index idx_bc_detail_destination_city_id
	on booking_commission_detail (bc_detail_destination_city_id);

create index idx_bc_detail_bc_id
	on booking_commission_detail (bc_detail_bc_id);

create index idx_bc_detail_commodity_code
	on booking_commission_detail (bc_detail_commodity_code);

create index idx_bc_detail_product_name
	on booking_commission_detail (bc_detail_product_name);



create table booking_commission_tier_base
(
    bc_tier_base_id                int auto_increment
        primary key,
    bc_tier_base_bc_id             int    default 0 not null,
    bc_tier_base_start             double default 0 not null,
    bc_tier_base_end               double default 0 not null,
    bc_tier_base_commission_amount double default 0 not null
);

create index idx_bc_tier_base_bc_id
    on booking_commission_tier_base (bc_tier_base_bc_id);



