-- CARGO

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'cargo_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cargo_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cargo_list_view', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cargo_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cargo_create', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cargo_create');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cargo_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cargo_detail_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cargo_detail_edit', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cargo_detail_edit');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('cargo_enable','cargo_list_view','cargo_create','cargo_detail_view','cargo_detail_edit')
	AND ar.account_role_type IN ('console','sub-console');