-- BOOKING V3
-- insert permission
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'booking_view_list', NOW(), (SELECT id FROM permission WHERE name = 'booking_feature_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'booking_view_list');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'booking_view_detail', NOW(), (SELECT id FROM permission WHERE name = 'booking_feature_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'booking_view_detail');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'booking_edit', NOW(), (SELECT id FROM permission WHERE name = 'booking_feature_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'booking_edit');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'booking_create_manual', NOW(), (SELECT id FROM permission WHERE name = 'booking_feature_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'booking_create_manual');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'booking_create_manual_for_corporate', NOW(), (SELECT id FROM permission WHERE name = 'booking_feature_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'booking_create_manual_for_corporate');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'booking_create_shipment_id', NOW(), (SELECT id FROM permission WHERE name = 'booking_feature_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'booking_create_shipment_id');

-- insert role permission (mapping permission)
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('booking_view_list', 'booking_view_detail', 'booking_edit')
	AND ar.account_role_type IN ('internal','console','sub-console','pos','client');


INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name = 'booking_create_manual'
	AND ar.account_role_type IN ('pos','client');


INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name = 'booking_create_manual_for_corporate'
	AND ar.account_role_type IN ('pos','internal');


INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name = 'booking_create_shipment_id'
	AND ar.account_role_type = 'pos';

