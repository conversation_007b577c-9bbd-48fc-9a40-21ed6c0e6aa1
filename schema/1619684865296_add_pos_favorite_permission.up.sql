-- POS FAVORITE

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'support_pos_favourite_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'support_pos_favourite_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'support_pos_favourite_subscriber_view_list', NOW(), (SELECT id FROM permission WHERE name = 'support_pos_favourite_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'support_pos_favourite_subscriber_view_list');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'support_pos_favourite_subscriber_edit_detail', NOW(), (SELECT id FROM permission WHERE name = 'support_pos_favourite_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'support_pos_favourite_subscriber_edit_detail');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'support_pos_favourite_subscriber_edit_default_discount', NOW(), (SELECT id FROM permission WHERE name = 'support_pos_favourite_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'support_pos_favourite_subscriber_edit_default_discount');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'support_pos_favourite_subscriber_outstanding_view_list', NOW(), (SELECT id FROM permission WHERE name = 'support_pos_favourite_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'support_pos_favourite_subscriber_outstanding_view_list');


-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('support_pos_favourite_enable','support_pos_favourite_subscriber_view_list','support_pos_favourite_subscriber_edit_detail','support_pos_favourite_subscriber_edit_default_discount','support_pos_favourite_subscriber_outstanding_view_list')
	AND ar.account_role_type IN ('pos');