INSERT INTO permission (name,created_at)
	SELECT 'dtpol_config_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'dtpol_config_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'dtpol_config_list_view', NOW(), (SELECT id FROM permission WHERE name = 'dtpol_config_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'dtpol_config_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'dtpol_config_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'dtpol_config_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'dtpol_config_detail_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'dtpol_config_detail_edit', NOW(), (SELECT id FROM permission WHERE name = 'dtpol_config_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'dtpol_config_detail_edit');


-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('dtpol_config_enable','dtpol_config_list_view','dtpol_config_detail_view','dtpol_config_detail_edit')
	AND ar.account_role_type IN ('internal');