UPDATE configurable_rule SET cr_rules ='rules-0.31' WHERE cr_product_type ='BIGPACK' AND  cr_rules != 'rules-0.31';

INSERT INTO configurable_rule
( cr_product_type, cr_flag, cr_rules, cr_excluded_booked_for, cr_created_by, cr_created_at, cr_updated_at)
SELECT 'JUMBOPACK', 'default', 'rules-0.31', '{"pos":false,"client":[0]}', 1, now(), NULL
FROM DUAL
WHERE NOT EXISTS (
    SELECT cr_product_type FROM configurable_rule WHERE cr_product_type = 'JUMBOPACK'
);

UPDATE configurable_rule SET cr_rules ='rules-0.31' WHERE cr_product_type ='JUMBOPACK' AND  cr_rules != 'rules-0.31';