CREATE TABLE IF NOT EXISTS `region` (
    `region_id` int NOT NULL AUTO_INCREMENT,
    `region_code` varchar(100) NOT NULL DEFAULT '' UNIQUE,
    `region_name` varchar(150) NOT NULL,
    `region_status` ENUM('ACTIVE','INACTIVE') NOT NULL DEFAULT 'ACTIVE',
    `region_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `region_updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`region_id`),
    UNIQUE KEY (`region_code`)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8;
-- CREATE INDEX region_status_IDX USING BTREE ON `region` (region_status);
    SELECT IF (
        EXISTS(
        SELECT distinct index_name FROM INFORMATION_SCHEMA.STATISTICS
            WHERE table_schema=DATABASE() AND table_name='region' AND index_name='region_status_IDX'
        ),
        "select 'index_exists'",
        'CREATE INDEX region_status_IDX USING BTREE ON `region` (region_status)') into @a
    ;

    PREPARE stmt1 FROM @a;
    EXECUTE stmt1;
    DEALLOCATE PREPARE stmt1;
    