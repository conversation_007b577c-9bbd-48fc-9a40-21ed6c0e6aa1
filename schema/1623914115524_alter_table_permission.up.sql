-- TOPUP MANUAL APPROVAL

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'topup_manual_approval_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_manual_approval_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'topup_manual_approval_list_view', NOW(), (SELECT id FROM permission WHERE name = 'topup_manual_approval_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_manual_approval_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'topup_manual_approval_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'topup_manual_approval_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_manual_approval_detail_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'topup_manual_approval_approval', NOW(), (SELECT id FROM permission WHERE name = 'topup_manual_approval_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_manual_approval_approval');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('topup_manual_approval_enable','topup_manual_approval_list_view','topup_manual_approval_detail_view','topup_manual_approval_approval')
	AND ar.account_role_type IN ('internal') AND ar.account_role_name IN ('admin_finance','admin_finance_approval');