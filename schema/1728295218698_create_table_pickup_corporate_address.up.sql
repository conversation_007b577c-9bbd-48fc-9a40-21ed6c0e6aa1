CREATE TABLE IF NOT EXISTS `pickup_corporate_address` (
    `pca_id` BIGINT AUTO_INCREMENT NOT NULL,
    `pca_client_id` BIGINT NOT NULL,
    `pca_is_default` B<PERSON>OLEAN DEFAULT FALSE,
    `pca_name` VARCHAR(100) NOT NULL,
    `pca_latlon` VARCHAR(50) NOT NULL,
    `pca_phone_number` VARCHAR(20) NOT NULL,
    `pca_address` VARCHAR(255) NOT NULL,
    `pca_pic_name` VARCHAR(255) NOT NULL,
    `pca_district_id` BIGINT NOT NULL,
    `pca_created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
    `pca_created_by` BIGINT NOT NULL,
    `pca_created_name` VARCHAR(255) NOT NULL,
    `pca_updated_at` TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `pca_updated_by` BIGINT NOT NULL,
    `pca_updated_name` VARCHAR(255) NOT NULL,
    <PERSON><PERSON>AR<PERSON>Y (`pca_id`),
    <PERSON><PERSON>Y `idx_client_id`(`pca_client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE INDEX idx_pca_client_id ON pickup_corporate_address(pca_client_id);