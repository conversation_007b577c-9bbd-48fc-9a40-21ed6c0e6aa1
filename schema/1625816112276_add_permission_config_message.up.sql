-- insert permission message_config_enable
INSERT INTO permission (name, created_at)
	SELECT 'message_config_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'message_config_enable');

-- insert permission message_config_add
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'message_config_add', NOW(), (SELECT id FROM permission WHERE name = 'message_config_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'message_config_add');

-- insert permission message_config_edit
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'message_config_edit', NOW(), (SELECT id FROM permission WHERE name = 'message_config_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'message_config_edit');

-- insert permission message_config_list_view
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'message_config_list_view', NOW(), (SELECT id FROM permission WHERE name = 'message_config_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'message_config_list_view');

-- insert permission message_config_detail_view
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'message_config_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'message_config_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'message_config_detail_view');


-- insert mapping permission for parent
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'message_config_enable',
        'message_config_add',
        'message_config_edit',
        'message_config_list_view',
        'message_config_detail_view'
    )
	AND ar.account_role_type = 'internal';