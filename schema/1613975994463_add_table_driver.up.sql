
 create table driver
(
	driver_id int auto_increment,
	driver_name varchar(50) default '' null,
	driver_phone_number varchar(45) default '' null,
	driver_ref_id int default 0 null,
	driver_created_at timestamp default now() null,
	driver_created_by int default 0 null,
	driver_updated_at timestamp null,
	driver_updated_by int default 0 null,
	constraint driver_pk
		primary key (driver_id)
);

create index idx_driver_id
	on driver (driver_id);

create index idx_driver_name
	on driver (driver_name);

create index idx_driver_ref_id
	on driver (driver_ref_id);
