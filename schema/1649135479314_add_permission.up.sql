 -- STI
 -- insert to table permission
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'sti_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'sti_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'sti_detail_view');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('sti_detail_view')
	AND ar.account_role_type IN ('console')
    AND ar.account_role_name IN ('admin_console', 'owner_console', 'manager_console', 'outgoing_console', 'sti_console', 'ops_hq_console');

-- STI-DEST
 -- insert to table permission
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'sti_dest_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'sti_dest_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'sti_dest_detail_view');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('sti_dest_detail_view')
	AND ar.account_role_type IN ('console')
    AND ar.account_role_name IN ('admin_console', 'owner_console', 'manager_console', 'incoming_console', 'stidest_console', 'ops_hq_console');

-- Handover
 -- insert to table permission
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'handover_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'handover_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'handover_detail_view');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('handover_detail_view')
	AND ar.account_role_type IN ('console', 'sub-console')
    AND ar.account_role_name IN ('admin_console', 'owner_console', 'manager_console', 'incoming_console', 'lmd_console', 'owner_subconsole', 'manager_subconsole', 'admin_subconsole', 'incoming_subconsole', 'lmd_subconsole', 'ops_hq_console');