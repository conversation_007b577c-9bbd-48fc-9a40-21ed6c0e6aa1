-- insert permission report_enable
INSERT INTO permission (name, created_at)
	SELECT 'report_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'report_enable');

-- insert permission report_view_stt_report_card
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'report_view_stt_report_card', NOW(), (SELECT id FROM permission WHERE name = 'report_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'report_view_stt_report_card');

-- insert permission report_view_manual_stt_report_card
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'report_view_manual_stt_report_card', NOW(), (SELECT id FROM permission WHERE name = 'report_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'report_view_manual_stt_report_card');

-- insert permission report_view_transaction_report_card
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'report_view_transaction_report_card', NOW(), (SELECT id FROM permission WHERE name = 'report_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'report_view_transaction_report_card');

-- insert permission report_view_fpr_report_card
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'report_view_fpr_report_card', NOW(), (SELECT id FROM permission WHERE name = 'report_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'report_view_fpr_report_card');

-- insert permission report_view_misroute_report_card
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'report_view_misroute_report_card', NOW(), (SELECT id FROM permission WHERE name = 'report_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'report_view_misroute_report_card');

-- insert permission report_view_shortland_report_card
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'report_view_shortland_report_card', NOW(), (SELECT id FROM permission WHERE name = 'report_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'report_view_shortland_report_card');

-- insert permission report_view_destination_incoming_report_card
INSERT INTO permission (name, created_at, parent_id)
	SELECT 'report_view_destination_incoming_report_card', NOW(), (SELECT id FROM permission WHERE name = 'report_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'report_view_destination_incoming_report_card');


-- insert mapping permission for internal
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'report_enable',
        'report_view_stt_report_card',
        'report_view_manual_stt_report_card',
        'report_view_transaction_report_card',
        'report_view_fpr_report_card',
        'report_view_misroute_report_card',
        'report_view_shortland_report_card',
        'report_view_destination_incoming_report_card'
    )
	AND ar.account_role_type = 'internal';

-- insert mapping permission for pos
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'report_enable',
        'report_view_stt_report_card',
        'report_view_transaction_report_card',
        'report_view_fpr_report_card'
    )
	AND ar.account_role_type = 'pos';

-- insert mapping permission for sub-console
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'report_enable'
    )
	AND ar.account_role_type = 'sub-console';

-- insert mapping permission for console
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'report_enable',
        'report_view_stt_report_card',
        'report_view_misroute_report_card',
        'report_view_shortland_report_card',
        'report_view_destination_incoming_report_card'
    )
	AND ar.account_role_type = 'console';

-- insert mapping permission for client branch & parent
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'report_enable',
        'report_view_stt_report_card',
        'report_view_manual_stt_report_card',
        'report_view_fpr_report_card'
    )
    AND ar.account_role_client_type IN ('parent', 'branch');
