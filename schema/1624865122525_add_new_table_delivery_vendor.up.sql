CREATE TABLE delivery_vendor (
	delivery_vendor_id INT auto_increment NOT NULL,
	delivery_vendor_name varchar(100) NOT NULL,
	delivery_vendor_partner_id INT NOT NULL,
	delivery_vendor_partner_type ENUM('console','sub-console') NOT NULL,
	delivery_vendor_created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
	delivery_vendor_created_by INT NOT NULL,
	CONSTRAINT delivery_vendor_PK PRIMARY KEY (delivery_vendor_id),
	CONSTRAINT delivery_vendor_UN UNIQUE KEY (delivery_vendor_name,delivery_vendor_partner_id,delivery_vendor_partner_type)
)
ENGINE=InnoDB
DEFAULT CHARSET=latin1
COLLATE=latin1_swedish_ci;
CREATE INDEX delivery_vendor_delivery_vendor_partner_id_IDX USING BTREE ON delivery_vendor (delivery_vendor_partner_id);
CREATE INDEX delivery_vendor_delivery_vendor_name_IDX USING BTREE ON delivery_vendor (delivery_vendor_name);
