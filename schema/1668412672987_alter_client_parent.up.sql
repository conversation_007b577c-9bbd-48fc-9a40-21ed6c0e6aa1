ALTER TABLE `client`
ADD `client_odoo_id` int(11) default 0,
ADD `client_company_size` enum('Small','Medium','Big','Micro') DEFAULT NULL,
ADD `client_efaktur_with_nik` tinyint(1) default 0,
ADD `client_nik_id` VARCHAR(50) NOT NULL default '',
ADD `client_nik_name` VARCHAR(255) NOT NULL default '',
ADD `client_nik_address` VARCHAR(255) NOT NULL default '',
ADD `client_taxpayer_name` VARCHAR(255) NOT NULL default '',
ADD `client_taxpayer_address` VARCHAR(255) NOT NULL default '',
ADD `client_tax_transaction_no` VARCHAR(255) NOT NULL default '',
ADD `client_billing_wa_number` VARCHAR(50) NOT NULL default '',
ADD `client_billing_email` TEXT,
ADD `client_billing_schedule_so` enum('Daily','Weekly','Bi-Weekly','Monthly') DEFAULT NULL,
ADD `client_billing_payment_period` enum('07 Days','14 Days','30 Days','45 Days','40 Days','60 Days') DEFAULT NULL,
ADD `client_contract_start_date` timestamp null DEFAULT NULL,
ADD `client_contract_end_date` timestamp null DEFAULT NULL,
ADD `client_contract_attachment_url` TEXT;
