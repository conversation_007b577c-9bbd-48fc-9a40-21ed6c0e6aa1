INSERT INTO permission (name,created_at,parent_id)
	SELECT 'shipment_bulk_booking_detail_edit', NOW(), (SELECT id FROM permission WHERE name = 'shipment_bulk_booking_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'shipment_bulk_booking_detail_edit');
    
-- insert mapping permission for parent
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'user_management_enable',
        'user_management_account_create',
        'user_management_account_detail_edit',
        'user_management_account_detail_view',
        'user_management_account_list_view',
        'check_tariff_enable',
        'check_tariff_list_view',
        'check_tariff_client_filter_view',
        'check_tariff_detail_view',
        'check_tariff_button_booking_view',
        'stt_tracking_enable',
        'shipment_bulk_booking_enable',
        'shipment_bulk_booking_detail_edit',
        'shipment_bulk_booking_detail_history_view',
        'shipment_bulk_booking_list_history_view',
        'shipment_bulk_booking_create',
        'saldo_enable',
        'saldo_withdraw_view_detail',
        'saldo_withdraw_request_detail',
        'saldo_beneficiary_account_view_detail',
        'saldo_beneficiary_account_add_detail',
        'saldo_beneficiary_account_edit_detail',
        'cod_enable',
        'cod_withdraw_view_detail',
        'cod_withdraw_request_detail',
        'cod_beneficiary_account_view_detail',
        'cod_beneficiary_account_add_detail',
        'cod_beneficiary_account_edit_detail',
        'bulk_upload_enable',
        'bulk_upload_create',
        'bulk_upload_list_view',
        'bulk_upload_detail_view',
        'bulk_upload_type_role',
        'bulk_upload_type_account'
        )
	AND ar.account_role_client_type = "branch";