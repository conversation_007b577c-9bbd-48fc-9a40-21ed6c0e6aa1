ALTER TABLE bulk ADD partner_id INT DEFAULT 0 NULL;
ALTER TABLE bulk ADD booked_by INT DEFAULT 0 NULL;
ALTER TABLE bulk ADD booked_type varchar(100) NULL;
CREATE INDEX bulk_partner_id_IDX USING BTREE ON bulk (partner_id);
CREATE INDEX bulk_booked_by_IDX USING BTREE ON bulk (booked_by);
CREATE INDEX bulk_booked_type_IDX USING BTREE ON bulk (booked_type);


CREATE TABLE bulk_stt (
  bulk_stt_id int(11) NOT NULL AUTO_INCREMENT,
  bulk_stt_success text,
  PRIMARY KEY (bulk_stt_id)
) ENGINE=InnoDB DEFAULT CHARSET=latin1