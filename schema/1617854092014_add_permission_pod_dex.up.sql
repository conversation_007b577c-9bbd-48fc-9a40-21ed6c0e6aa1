
-- permission pod dex enable
INSERT INTO permission (name, created_at)
  SELECT
    'pod_dex_enable',
    NOW()
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'pod_dex_enable');

-- permission pod dex view list
INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'pod_dex_list',
    NOW(),
    (SELECT id FROM permission WHERE name = 'pod_dex_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'pod_dex_list');

-- permission pod dex create
INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'pod_dex_update',
    NOW(),
    (SELECT id FROM permission WHERE name = 'pod_dex_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'pod_dex_update');

-- INSERT new role pod dex permission --
INSERT INTO role_permission (permission_id, account_role_id)
SELECT p.id, ar.account_role_id
FROM permission p
         CROSS JOIN account_role ar
WHERE p.name IN (
                 'pod_dex_enable',
                 'pod_dex_update',
                 'pod_dex_list')
  AND ar.account_role_type IN
      ('pos', 'console', 'sub-console');