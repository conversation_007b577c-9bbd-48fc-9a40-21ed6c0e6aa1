ALTER TABLE hub ADD COLUMN hub_city_code VARCHAR(45) DEFAULT '';
ALTER TABLE hub ADD COLUMN hub_district_code VARCHAR(45) DEFAULT '';
ALTER TABLE hub ADD COLUMN hub_address text;
ALTER TABLE hub ADD COLUMN hub_contact_name VA<PERSON>HAR(255) DEFAULT '';
ALTER TABLE hub ADD COLUMN hub_contact_phone VARCHAR(20) DEFAULT '';
ALTER TABLE hub ADD COLUMN hub_contact_email VARCHAR(100) DEFAULT '';
ALTER TABLE hub ADD COLUMN hub_status VARCHAR(100) DEFAULT '';
ALTER TABLE hub ADD COLUMN hub_created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE hub ADD COLUMN hub_created_by INT(11) NOT NULL DEFAULT 0;
ALTER TABLE hub ADD COLUMN hub_updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE hub ADD COLUMN hub_updated_by INT(11) NOT NULL DEFAULT 0;

CREATE INDEX hub_city_code_idx USING BTREE ON hub (hub_city_code);
CREATE INDEX hub_district_code_idx USING BTREE ON hub (hub_district_code);
CREATE INDEX hub_status_idx USING BTREE ON hub (hub_status);
