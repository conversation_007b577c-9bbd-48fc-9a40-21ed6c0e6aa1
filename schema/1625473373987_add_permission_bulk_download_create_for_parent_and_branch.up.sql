INSERT INTO permission (name,created_at,parent_id)
	SELECT 'bulk_download_create', NOW(), (SELECT id FROM permission WHERE name = 'bulk_download_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_download_create');
    
-- insert mapping permission for parent
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name = 'bulk_download_create'
	AND ar.account_role_client_type IN ("parent", "branch");