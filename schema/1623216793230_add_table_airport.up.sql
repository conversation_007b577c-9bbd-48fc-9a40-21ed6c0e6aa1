
create table airport
(
	airport_id int auto_increment,
	airport_code varchar(50) default '' not null,
	airport_name varchar(150) default '' not null,
	airport_created_at timestamp default now() not null,
	constraint airport_pk
		primary key (airport_id)
);

create index idx_airport_code
	on airport (airport_code);

create table airport_city
(
	airport_city_id int auto_increment,
	airport_city_airport_code varchar(50) default '' not null,
	airport_city_city_code varchar(50) default '' null,
	constraint airport_city_pk
		primary key (airport_city_id)
);

create index idx_airport_city_airport_code
	on airport_city (airport_city_airport_code);

create index idx_airport_city_city_code
	on airport_city (airport_city_city_code);

