INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('handover_enable', NULL, NOW(), 0);
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('handover_list_view', NULL, NOW(), (SELECT p.id FROM permission p WHERE p.`name` = "handover_enable"));
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('handover_create', NULL, NOW(), (SELECT p.id FROM permission p WHERE p.`name` = "handover_enable"));

INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "handover_enable"), 2);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "handover_list_view"), 2);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "handover_create"), 2);
