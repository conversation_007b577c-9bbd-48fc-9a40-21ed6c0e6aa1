-- Delete enable permission for account role console
DELETE role_permission
FROM role_permission
INNER JOIN permission ON role_permission.permission_id = permission.id
INNER JOIN account_role ON role_permission.account_role_id = account_role.account_role_id
WHERE account_role.account_role_type = "console" 
AND permission.name = "shipment_stt_manual_enable";

-- Delete list permission for account role console
DELETE role_permission
FROM role_permission
INNER JOIN permission ON role_permission.permission_id = permission.id
INNER JOIN account_role ON role_permission.account_role_id = account_role.account_role_id
WHERE account_role.account_role_type = "console" 
AND permission.name = "shipment_stt_manual_list";

-- Delete create permission for account role console
DELETE role_permission
FROM role_permission
INNER JOIN permission ON role_permission.permission_id = permission.id
INNER JOIN account_role ON role_permission.account_role_id = account_role.account_role_id
WHERE account_role.account_role_type = "console" 
AND permission.name = "shipment_stt_manual_create";

-- Delete download permission for account role console
DELETE role_permission
FROM role_permission
INNER JOIN permission ON role_permission.permission_id = permission.id
INNER JOIN account_role ON role_permission.account_role_id = account_role.account_role_id
WHERE account_role.account_role_type = "console" 
AND permission.name = "shipment_stt_manual_download";
