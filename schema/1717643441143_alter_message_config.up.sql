ALTER TABLE `message_configuration` add `msg_conf_destination_city` varchar(250) NOT NULL DEFAULT '' AFTER `msg_conf_origin_city_exclude`;
ALTER TABLE `message_configuration` add `msg_conf_destination_city_exclude` varchar(250) NOT NULL DEFAULT '' AFTER `msg_conf_destination_city`;
ALTER TABLE `message_configuration` 
    ADD INDEX `idx_msg_conf_destination_city` (`msg_conf_destination_city`),
    ADD INDEX `idx_msg_conf_destination_city_exclude` (`msg_conf_destination_city_exclude`);