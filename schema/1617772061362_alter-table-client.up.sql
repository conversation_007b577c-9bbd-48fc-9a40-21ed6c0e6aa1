ALTER TABLE client ADD client_type ENUM("B2B","B2C","C2C","SMB") NOT NULL;
ALTER TABLE client ADD client_bank_name varchar(100) NULL;
ALTER TABLE client ADD client_bank_account_name varchar(100) NULL;
ALTER TABLE client ADD client_bank_account_number varchar(100) NULL;
ALTER TABLE client ADD client_bank_account_email varchar(100) NULL;
ALTER TABLE client ADD client_location_district varchar(100) NULL;
ALTER TABLE client ADD client_is_cod TINYINT DEFAULT 0 NULL;
ALTER TABLE client ADD client_contact_name varchar(100) NULL;
ALTER TABLE client ADD client_contact_job_title varchar(100) NULL;
ALTER TABLE client ADD client_contact_email varchar(100) NULL;
ALTER TABLE client ADD client_contact_phone varchar(100) NULL;
ALTER TABLE client ADD client_partner_pos_id INT NULL;
ALTER TABLE client ADD client_partner_pos_commission DOUBLE NULL;
ALTER TABLE client ADD client_partner_pos_quantifier_commission varchar(255) NULL;
ALTER TABLE client ADD client_is_banned TINYINT DEFAULT 0 NULL;
ALTER TABLE client ADD client_banned_reason TEXT NULL;
ALTER TABLE client ADD client_code_elexys varchar(100) NULL;
ALTER TABLE client ADD client_parent_id INT NULL;
