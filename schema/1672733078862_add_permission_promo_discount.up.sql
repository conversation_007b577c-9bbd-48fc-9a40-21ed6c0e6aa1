INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'promo_discount_enable', '', NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'promo_discount_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'promo_discount_create', '', NOW(), (select id from permission where name = 'promo_discount_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'promo_discount_create');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'promo_discount_list_view', '', NOW(), (select id from permission where name = 'promo_discount_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'promo_discount_list_view');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'promo_discount_view_detail', '', NOW(), (select id from permission where name = 'promo_discount_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'promo_discount_view_detail');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'promo_discount_detail_edit', '', NOW(), (select id from permission where name = 'promo_discount_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'promo_discount_detail_edit');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'promo_discount_detail_duplicate', '', NOW(), (select id from permission where name = 'promo_discount_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'promo_discount_detail_duplicate');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'promo_discount_detail_cancel', '', NOW(), (select id from permission where name = 'promo_discount_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'promo_discount_detail_cancel');