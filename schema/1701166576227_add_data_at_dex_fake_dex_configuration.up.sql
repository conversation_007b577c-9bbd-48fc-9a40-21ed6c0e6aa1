INSERT INTO dex_fake_dex_configuration (dfdc_id, dfdc_name, dfdc_description, dfdc_type, dfdc_reason_code, dfdc_reason_category, dfdc_param_start_time, dfdc_param_end_time, dfdc_param_exclude_weekend_holiday, dfdc_office_start_time, dfdc_office_end_time, dfdc_office_exclude_weekend_holiday, dfdc_genesis, dfdc_driver_app, dfdc_status, dfdc_created_at, dfdc_created_by, dfdc_updated_at, dfdc_updated_by) VALUES('5cf9daf6-8dc3-11ee-b9d1-0242ac120002', 'Pengiriman Diluar Jam Kirim Kurir', 'Otomasi ketika Alasan DEX adalah Pengiriman Diluar Jam Kirim Kurir', 'reason_courier_outside_ops_hour', 'RES62', 'lainnya', '08:00', '22:00', 0, ' ', ' ', 0, 0, 0, 'active', '2023-11-09 13:00:00', 1, '2023-12-29 10:37:21', 1);
INSERT INTO dex_fake_dex_configuration (dfdc_id, dfdc_name, dfdc_description, dfdc_type, dfdc_reason_code, dfdc_reason_category, dfdc_param_start_time, dfdc_param_end_time, dfdc_param_exclude_weekend_holiday, dfdc_office_start_time, dfdc_office_end_time, dfdc_office_exclude_weekend_holiday, dfdc_genesis, dfdc_driver_app, dfdc_status, dfdc_created_at, dfdc_created_by, dfdc_updated_at, dfdc_updated_by) VALUES('64036060-8dc3-11ee-b9d1-0242ac120002', 'Asal DEX', 'Otomasi pengecekan asal DEX STT', 'source_dex', '', '', ' ', ' ', 0, ' ', ' ', 0, 1, 0, 'active', '2023-11-09 13:00:00', 1, '2023-12-29 10:36:30', 1);
INSERT INTO dex_fake_dex_configuration (dfdc_id, dfdc_name, dfdc_description, dfdc_type, dfdc_reason_code, dfdc_reason_category, dfdc_param_start_time, dfdc_param_end_time, dfdc_param_exclude_weekend_holiday, dfdc_office_start_time, dfdc_office_end_time, dfdc_office_exclude_weekend_holiday, dfdc_genesis, dfdc_driver_app, dfdc_status, dfdc_created_at, dfdc_created_by, dfdc_updated_at, dfdc_updated_by) VALUES('7d79be4a-e4ee-40a7-a06a-c0814fe830ab', 'Lokasi Tutup', 'Otomasi ketika Alasan DEX adalah lokasi tutup', 'reason_closed_address', 'RES73', 'customer', '08:00', '20:00', 0, '08:00', '18:00', 1, 0, 0, 'active', '2023-11-09 13:00:00', 1, '2023-12-29 10:35:39', 1);