
alter table partner
	add partner_external_code varchar(100) default '' not null after code;

alter table partner
	add partner_contract_start timestamp default now() not null after `long`;

alter table partner
	add partner_contract_end timestamp default now() not null after partner_contract_start;

alter table partner
	add partner_is_banned bool default false not null after partner_contract_end;

alter table partner
	add partner_banned_reason text not null after partner_is_banned;

alter table partner
	add partner_contact_name varchar(150) default '' not null after partner_banned_reason;

alter table partner
	add partner_contact_job_title varchar(50) default '' not null after partner_contact_name;

alter table partner
	add partner_contact_email varchar(50) default '' not null after partner_contact_job_title;

alter table partner
	add partner_contact_phone_number text not null after partner_contact_email;

alter table partner
	add partner_beneficiary_bank_name varchar(50) default '' not null after partner_contact_phone_number;

alter table partner
	add partner_beneficiary_account_number varchar(50) default '' not null after partner_beneficiary_bank_name;

alter table partner
	add partner_beneficiary_account_name varchar(150) default '' not null after partner_beneficiary_account_number;

alter table partner
	add partner_beneficiary_email varchar(50) default '' not null after partner_beneficiary_account_name;

alter table partner
	add created_by int default 0 not null after created_at;

alter table partner
	add updated_by int default 0 not null after updated_at;

alter table partner
	add partner_parent_partner_type enum('console', 'sub-console') default 'console' not null after partner_type;

create index idx_partner_contract_status
	on partner (partner_contract_status);

create index idx_partner_is_banned
	on partner (partner_is_banned);

create index idx_partner_parent_partner_type
	on partner (partner_parent_partner_type);

create index idx_partner_contract_end
	on partner (partner_contract_end);

create index idx_partner_contract_start
	on partner (partner_contract_start);

