CREATE TABLE IF NOT EXISTS pickup_schedule (
    ps_id INT AUTO_INCREMENT PRIMARY KEY,
    ps_name VARCHAR(255) NOT NULL,
    ps_client_parent_id INT NOT NULL,
    ps_client_id INT NOT NULL,
    ps_city_id VARCHAR(255) NOT NULL,
    ps_district_id VARCHAR(255) NOT NULL,
    ps_address VARCHAR(255) NOT NULL,
    ps_geoloc VARCHAR(255) NOT NULL,
    ps_courier_note VARCHAR(255) NULL,
    ps_transport_type VARCHAR(255) NOT NULL,
    ps_service_codes VARCHAR(255) NOT NULL,
    ps_estimate_total_koli INT NOT NULL,
    ps_estimate_total_tonase INT NOT NULL,
    ps_start TIMESTAMP NOT NULL,
    ps_end TIMESTAMP NOT NULL,
    ps_time VARCHAR(8) NOT NULL,
    ps_city_timezone VARCHAR(20) NOT NULL,
    ps_interval_week INT NOT NULL,
    ps_repeat_days VARCHAR(255) NOT NULL,
    ps_last_run TIMESTAMP NOT NULL,
    ps_next_run TIMESTAMP NOT NULL,
    ps_created_at timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    ps_created_by int NOT NULL DEFAULT '0',
    ps_updated_at timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    ps_updated_by int NOT NULL DEFAULT '0',
    ps_status VARCHAR(255) NOT NULL
);