-- DEDUCT ADD SALDO

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'deduct_add_saldo_manual_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'deduct_add_saldo_manual_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'deduct_add_saldo_manual_list_view', NOW(), (SELECT id FROM permission WHERE name = 'deduct_add_saldo_manual_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'deduct_add_saldo_manual_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'deduct_add_saldo_manual_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'deduct_add_saldo_manual_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'deduct_add_saldo_manual_detail_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'deduct_add_saldo_manual_create', NOW(), (SELECT id FROM permission WHERE name = 'deduct_add_saldo_manual_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'deduct_add_saldo_manual_create');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('deduct_add_saldo_manual_enable','deduct_add_saldo_manual_list_view','deduct_add_saldo_manual_detail_view', 'deduct_add_saldo_manual_create')
	AND ar.account_role_type IN ('internal');