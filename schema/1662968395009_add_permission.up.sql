INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'claim_enable', '', NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'claim_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'claim_list_view', '', NOW(), (select id from permission where name = 'claim_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'claim_list_view');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'claim_detail_view', '', NOW(), (select id from permission where name = 'claim_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'claim_detail_view');