INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'sto_sc_enable', '', NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'sto_sc_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'sto_sc_list_view', '', NOW(), (select id from permission where name = 'sto_sc_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'sto_sc_list_view');