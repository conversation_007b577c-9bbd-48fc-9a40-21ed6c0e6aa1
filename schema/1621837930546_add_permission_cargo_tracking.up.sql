-- insert new permission cargo tracking
-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'cargo_tracking_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cargo_tracking_enable');


-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('cargo_tracking_enable')
	AND ar.account_role_type IN ('pos','client','internal','console','sub-console');

