INSERT INTO configurable_dtpol_product (cdp_name,cdp_description,cdp_commission_status,cdp_created_at,cdp_created_by,cdp_updated_by,cdp_updated_name,cdp_updated_at)
SELECT 'BOSSPACK','','active',NOW(),0,0,'',NOW()
WHERE NOT EXISTS(SELECT * FROM `configurable_dtpol_product` WHERE `cdp_name` = 'BOSSPACK');

INSERT INTO configurable_dtpol_product_detail (cdpd_cdp_id,cdpd_tier,cdpd_publish_rate_commission,cdpd_shipping_surcharge_commission,cdpd_cod_commission,cdpd_created_at,cdpd_updated_at) VALUES
	 ((SELECT cdp_id
		FROM configurable_dtpol_product
		WHERE `cdp_name` = 'BOSSPACK' LIMIT 1),1,30,0,1,NOW(),NOW()),
    ((SELECT cdp_id
		FROM configurable_dtpol_product
		WHERE `cdp_name` = 'BOSSPACK' LIMIT 1),2,10,0,1,NOW(),NOW()),
	 ((SELECT cdp_id
		FROM configurable_dtpol_product
		WHERE `cdp_name` = 'BOSSPACK' LIMIT 1),3,0,0,1,NOW(),NOW()),
	 ((SELECT cdp_id
		FROM configurable_dtpol_product
		WHERE `cdp_name` = 'BOSSPACK' LIMIT 1),4,0,0,1,NOW(),NOW());