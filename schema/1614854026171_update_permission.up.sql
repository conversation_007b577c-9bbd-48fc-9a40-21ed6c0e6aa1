UPDATE `role_permission` SET `account_role_id` = 3 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "sti_sc_enable");
UPDATE `role_permission` SET `account_role_id` = 3 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "sti_sc_list_view");
UPDATE `role_permission` SET `account_role_id` = 3 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "sti_sc_create");

UPDATE `role_permission` SET `account_role_id` = 2 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "sti_enable");
UPDATE `role_permission` SET `account_role_id` = 2 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "sti_list_view");
UPDATE `role_permission` SET `account_role_id` = 2 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "sti_create");

UPDATE `role_permission` SET `account_role_id` = 2 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "bagging_enable");
UPDATE `role_permission` SET `account_role_id` = 2 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "bagging_list_view");
UPDATE `role_permission` SET `account_role_id` = 2 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "bagging_create");
UPDATE `role_permission` SET `account_role_id` = 2 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "bagging_detail_view");
UPDATE `role_permission` SET `account_role_id` = 2 
WHERE `permission_id` = (SELECT id FROM permission WHERE `name`= "bagging_detail_edit");

INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_enable'), 3);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_list_view'), 3);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_create'), 3);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_detail_view'), 3);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) 
VALUES ((SELECT p.id FROM permission p WHERE p.name = 'bagging_detail_edit'), 3);