INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'AVI', 'LIVE ANIMAL', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'FRO', 'FROZEN GOODS', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'HUM', 'HUMAN REMAINS', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'PEF', 'FLOWERS', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'PEP', 'FRUITS & VEGETABLES', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'PER', 'PERISHABLE', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'PES', 'FISH/SEAFOOD', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'PIL', 'PHARMACEUTICAL', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'RFL', 'CLASS-5 FLAMMABLE LIQUIDS', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'RIS', 'INFECTIOUS SUBSTANCE', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'RLI', 'FULLY REGULATED LITHIUM ION BATTERIES (CLASS 9)', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'SEA', 'CORAL TEST', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'VAL', 'VALUABLE', 'active', NOW(), NOW();

INSERT INTO commodity_shc (commodity_shc_id, commodity_shc_code, commodity_shc_description, commodity_shc_status, commodity_shc_created_at, commodity_shc_updated_at)
SELECT uuid(), 'VUL', '-', 'active', NOW(), NOW();