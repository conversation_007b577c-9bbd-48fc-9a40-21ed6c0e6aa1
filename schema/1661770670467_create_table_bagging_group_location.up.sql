CREATE TABLE `bagging_group_location` (
  `bgl_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `bgl_destination_id` varchar(20) NOT NULL DEFAULT '',
  `bgl_destination_name` varchar(100) DEFAULT '',
  `bgl_region_id` varchar(20) NOT NULL DEFAULT '',
  `bgl_region_name` varchar(100) DEFAULT '',
  `bgl_status` varchar(20) DEFAULT '',
  PRIMARY KEY (`bgl_id`),
  KEY `idx_bgl_destination_id` (`bgl_destination_id`),
  KEY `idx_bgl_status` (`bgl_status`),
  KEY `idx_bgl_region_id` (`bgl_region_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;