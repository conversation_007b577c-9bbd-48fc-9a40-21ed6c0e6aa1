CREATE TABLE `configurable_dtpol_product` (
  `cdp_id` int(11) NOT NULL AUTO_INCREMENT,
  `cdp_name` varchar(100) NOT NULL,
  `cdp_description` text,
  `cdp_commission_status` enum('active','inactive') DEFAULT 'active',
  `cdp_created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cdp_created_by` int(11) NOT NULL,
  `cdp_updated_by` int(11) DEFAULT NULL,
  `cdp_updated_name` varchar(100) DEFAULT '',
  `cdp_updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`cdp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `configurable_dtpol_product_detail` (
  `cdpd_id` int(11) NOT NULL AUTO_INCREMENT,
  `cdpd_cdp_id` int(11) NOT NULL,
  `cdpd_tier` int(11) NOT NULL,
  `cdpd_publish_rate_commission` double DEFAULT '0',
  `cdpd_shipping_surcharge_commission` double DEFAULT '0',
  `cdpd_cod_commission` double DEFAULT '0',
  `cdpd_created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cdpd_updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`cdpd_id`),
  KEY `configurable_dtpol_product_detail_cdpd_cdp_id_IDX` (`cdpd_cdp_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


