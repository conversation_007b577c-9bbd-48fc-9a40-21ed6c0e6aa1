INSERT INTO permission (name,created_at)
	SELECT 'integration_monitoring_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'integration_monitoring_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'integration_monitoring_view', NOW(), (SELECT id FROM permission WHERE name = 'integration_monitoring_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'integration_monitoring_view');