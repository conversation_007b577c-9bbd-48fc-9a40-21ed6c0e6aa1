ALTER TABLE `bagging_group_location` ADD `bgl_created_by` INT UNSIGNED NULL DEFAULT '0';
ALTER TABLE `bagging_group_location` ADD `bgl_created_name` VARCHAR(100) NULL DEFAULT '';
ALTER TABLE `bagging_group_location` ADD `bgl_created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `bagging_group_location` ADD `bgl_updated_by` INT UNSIGNED NULL;
ALTER TABLE `bagging_group_location` ADD `bgl_updated_name` VARCHAR(100) NULL;
ALTER TABLE `bagging_group_location` ADD `bgl_updated_at` TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP;