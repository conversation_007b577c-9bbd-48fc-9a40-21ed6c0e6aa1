ALTER TABLE `partner` ADD `partner_referral_code` VA<PERSON>HAR(50) DEFAULT NULL AFTER `partner_parent_id`;
ALTER TABLE `partner` ADD `partner_referrer_code` VARCHAR(50) DEFAULT NULL AFTER `partner_referral_code`;
ALTER TABLE `partner` ADD `partner_referrer_pos_name` VARCHAR(100) NOT NULL DEFAULT '' AFTER `partner_referrer_code`;
ALTER TABLE `partner` ADD `partner_referrer_pos_code` VARCHAR(100) NOT NULL DEFAULT '' AFTER `partner_referrer_pos_name`;

ALTER TABLE `partner` ADD CONSTRAINT `idx_partner_referral_code` UNIQUE (`partner_referral_code`);
ALTER TABLE `partner` ADD INDEX `idx_partner_referrer_code` (`partner_referrer_code`);