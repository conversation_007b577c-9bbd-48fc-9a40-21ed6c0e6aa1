CREATE TABLE `configurable_dtpol_config` (
  `cdpc_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cdpc_is_more_than` TINYINT NOT NULL DEFAULT 0,
  `cdpc_is_less_then` TINYINT NOT NULL DEFAULT 0,
  `cdpc_city_type` enum('intercity','intracity') NOT NULL DEFAULT 'intercity',
  `cdpc_starting_time` int(11) NOT NULL DEFAULT 0,
  `cdpc_status` enum('ARR','DEP','STI-DEST','TRUCK-TRAIN') NOT NULL DEFAULT 'ARR',
  `cdpc_working_hour` int(11) NOT NULL DEFAULT 0,
  `cdpc_tier` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`cdpc_id`),
  KEY `idx_cdpc_is_more_than` (`cdpc_is_more_than`),
  <PERSON><PERSON>Y `idx_cdpc_is_less_then` (`cdpc_is_less_then`),
  KEY `idx_cdpc_city_type` (`cdpc_city_type`),
  KEY `idx_cdpc_starting_time` (`cdpc_starting_time`),
  KEY `idx_cdpc_status` (`cdpc_status`),
  KEY `idx_cdpc_working_hour` (`cdpc_working_hour`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
