CREATE TABLE `message_configuration` (
  `msg_conf_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `msg_conf_name` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_type` enum('WA Basic','WA Premium','SMS Basic','SMS Premium') NOT NULL,
  `msg_conf_stt_last_status` enum('BKD','POD','DEX','DEL') NOT NULL,
  `msg_conf_packet_type` enum('Non COD','COD') NOT NULL,
  `msg_conf_target` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_booked_for` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_product` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_origin_city` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_origin_city_exclude` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_pos_id` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_pos_id_exclude` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_client_id` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_client_id_exclude` varchar(250) NOT NULL DEFAULT '''',
  `msg_conf_schedule_periode_start` TIMESTAMP NULL DEFAULT NULL,
  `msg_conf_schedule_periode_end` TIMESTAMP NULL DEFAULT NULL,
  `msg_conf_message_text` TEXT NOT NULL ,
  `msg_conf_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `msg_conf_created_by` int(11) unsigned DEFAULT NULL,
  `msg_conf_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `msg_conf_updated_by` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`msg_conf_id`),
  KEY `idx_msg_conf_name` (`msg_conf_name`),
  KEY `idx_msg_conf_type` (`msg_conf_type`),
  KEY `idx_msg_conf_stt_last_status` (`msg_conf_stt_last_status`),
  KEY `idx_idx_msg_conf_target` (`msg_conf_target`),
  KEY `idx_msg_conf_schedule_periode_start` (`msg_conf_schedule_periode_start`),
  KEY `idx_msg_conf_schedule_periode_end` (`msg_conf_schedule_periode_end`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8