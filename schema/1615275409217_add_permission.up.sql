INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('delivery_enable', NULL, NOW(), 0);
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('delivery_view_list', NULL, NOW(), (SELECT p.id FROM permission p WHERE p.`name` = "delivery_enable"));
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('delivery_create', NULL, NOW(), (SELECT p.id FROM permission p WHERE p.`name` = "delivery_enable"));

INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "delivery_enable"), 2);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "delivery_view_list"), 2);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "delivery_create"), 2);


INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "delivery_enable"), 3);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "delivery_view_list"), 3);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "delivery_create"), 3);
