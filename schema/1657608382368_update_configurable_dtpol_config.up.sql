ALTER TABLE `configurable_dtpol_config` MODIFY `cdpc_status` ENUM("ARR","DEP","STI-DEST","TRUCK-TRAIN","DROPOFF_TRUCKING") DEFAULT "ARR" NOT NULL;

INSERT INTO `configurable_dtpol_config` (`cdpc_is_more_than`, `cdpc_is_less_then`, `cdpc_city_type`, `cdpc_starting_time`, `cdpc_status`, `cdpc_working_hour`, `cdpc_tier`, `cdpc_is_weekday`)
VALUES
(0, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 18, 1, 1),
(0, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 42, 2, 1),
(0, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 66, 3, 1),
(0, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 90, 4, 1),
(0, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 114, 5, 1),
(1, 0, 'intercity', 12, 'DROPOFF_TRUCKING', 24, 1, 1),
(1, 0, 'intercity', 12, 'DROPOFF_TRUCKING', 48, 2, 1),
(1, 0, 'intercity', 12, 'DROPOFF_TRUCKING', 72, 3, 1),
(1, 0, 'intercity', 12, 'DROPOFF_TRUCKING', 96, 4, 1),
(1, 0, 'intercity', 12, 'DROPOFF_TRUCKING', 120, 5, 1),
(1, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 24, 1, 0),
(1, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 48, 2, 0),
(1, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 72, 3, 0),
(1, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 96, 4, 0),
(1, 1, 'intercity', 12, 'DROPOFF_TRUCKING', 120, 5, 0);
