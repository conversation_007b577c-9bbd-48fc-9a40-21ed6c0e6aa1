INSERT INTO permission (name,created_at)
	SELECT 'topup_verification_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_verification_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'topup_verification_list_view', NOW(), (SELECT id FROM permission WHERE name = 'topup_verification_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_verification_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'topup_verification_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'topup_verification_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_verification_detail_view');


INSERT INTO permission (name,created_at,parent_id)
	SELECT 'topup_verification_verify', NOW(), (SELECT id FROM permission WHERE name = 'topup_verification_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_verification_verify');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('topup_verification_enable','topup_verification_list_view','topup_verification_detail_view', 'topup_verification_verify')
	AND ar.account_role_type IN ('internal');