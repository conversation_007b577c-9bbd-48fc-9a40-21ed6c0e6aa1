CREATE TABLE IF NOT EXISTS `bulk_booking_template` (
  `bbt_id_config` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `bbt_account_ref_id` int(10) unsigned NOT NULL DEFAULT 0,
  `bbt_account_ref_type` varchar(30) NOT NULL DEFAULT '',
  `bbt_header_mapping` text,
  `bbt_created_by` int(10) unsigned NOT NULL DEFAULT 0,
  `bbt_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `bbt_updated_by` int(10) unsigned NOT NULL DEFAULT 0,
  `bbt_updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`bbt_id_config`),
  KEY `idx_bbt_account_ref_id` (`bbt_account_ref_id`),
  KEY `idx_bbt_account_ref_type` (`bbt_account_ref_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

