-- Delete booking_create_manual permission for account_role_client_type parent
DELETE role_permission
FROM role_permission
INNER JOIN permission ON role_permission.permission_id = permission.id
INNER JOIN account_role ON role_permission.account_role_id = account_role.account_role_id
WHERE account_role.account_role_client_type = "parent" 
AND permission.name = "booking_create_manual";

-- Delete booking_edit permission for account_role_client_type parent
DELETE role_permission
FROM role_permission
INNER JOIN permission ON role_permission.permission_id = permission.id
INNER JOIN account_role ON role_permission.account_role_id = account_role.account_role_id
WHERE account_role.account_role_client_type = "parent" 
AND permission.name = "booking_edit";