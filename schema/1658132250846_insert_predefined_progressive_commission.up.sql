INSERT INTO configurable_progressive_commission (cpc_name,cpc_start_date,cpc_end_date,cpc_applied_to,cpc_term_condition,cpc_created_at,cpc_created_by,cpc_updated_at,cpc_updated_by)
	VALUES ('Progressive Commission August','2022-08-01 00:00:00','2022-08-31 23:59:59','pos','',now(),0,now(),0);


INSERT INTO configurable_progressive_commission_detail (cpcd_milestone_id,cpcd_milestone_limit,cpcd_milestone_reference,cpcd_commission_additional,cpcd_cpc_id)
	VALUES (1,10000000,'publish_rate,shipping_surcharge',0,(SELECT cpc_id FROM configurable_progressive_commission
WHERE cpc_name = 'Progressive Commission August'));
INSERT INTO configurable_progressive_commission_detail (cpcd_milestone_id,cpcd_milestone_limit,cpcd_milestone_reference,cpcd_commission_additional,cpcd_cpc_id)
	VALUES (2,25000000,'publish_rate,shipping_surcharge',5,(SELECT cpc_id FROM configurable_progressive_commission
WHERE cpc_name = 'Progressive Commission August'));
INSERT INTO configurable_progressive_commission_detail (cpcd_milestone_id,cpcd_milestone_limit,cpcd_milestone_reference,cpcd_commission_additional,cpcd_cpc_id)
	VALUES (3,50000000,'publish_rate,shipping_surcharge',7,(SELECT cpc_id FROM configurable_progressive_commission
WHERE cpc_name = 'Progressive Commission August'));
INSERT INTO configurable_progressive_commission_detail (cpcd_milestone_id,cpcd_milestone_limit,cpcd_milestone_reference,cpcd_commission_additional,cpcd_cpc_id)
	VALUES (4,100000000,'publish_rate,shipping_surcharge',9,(SELECT cpc_id FROM configurable_progressive_commission
WHERE cpc_name = 'Progressive Commission August'));
INSERT INTO configurable_progressive_commission_detail (cpcd_milestone_id,cpcd_milestone_limit,cpcd_milestone_reference,cpcd_commission_additional,cpcd_cpc_id)
	VALUES (5,250000000,'publish_rate,shipping_surcharge',11,(SELECT cpc_id FROM configurable_progressive_commission
WHERE cpc_name = 'Progressive Commission August'));
INSERT INTO configurable_progressive_commission_detail (cpcd_milestone_id,cpcd_milestone_limit,cpcd_milestone_reference,cpcd_commission_additional,cpcd_cpc_id)
	VALUES (6,1000000000000,'publish_rate,shipping_surcharge',13,(SELECT cpc_id FROM configurable_progressive_commission
WHERE cpc_name = 'Progressive Commission August'));
