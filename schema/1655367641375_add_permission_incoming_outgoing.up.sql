INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) VALUES ('internal_outgoing_incoming_enable', '', CURRENT_TIMESTAMP, 0); 
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) select 'internal_outgoing_incoming_list', '', CURRENT_TIMESTAMP, id from permission where name = 'internal_outgoing_incoming_enable';

INSERT INTO `role_permission` (`permission_id`, `account_role_id`) VALUES ((SELECT id FROM `permission` WHERE name = 'cargo_detail_edit'), 1);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) VALUES ((SELECT id FROM `permission` WHERE name = 'cargo_detail_view'), 1);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) VALUES ((SELECT id FROM `permission` WHERE name = 'bagging_detail_edit'), 1);
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) VALUES ((SELECT id FROM `permission` WHERE name = 'bagging_detail_view'), 1);