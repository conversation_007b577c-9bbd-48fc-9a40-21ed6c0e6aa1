
UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hai {consignee_name}! Paket COD dari {shipper_name} nomor {stt_number} sudah diterima oleh {recipient_name}! Silahkan cek bukti penerima berikut {proof_photo}. Terima kasih sudah menggunakan Lion Parcel! Ditunggu kiriman selanjutnya, ya!\n\n
Cobain aplikasi Lion Parcel, dijamin <PERSON>rani <PERSON> karena punya gratis Pick Up & Parcel Poin berkali-kali lipat! Yuk, download aplikasinya sekarang di https://bit.ly/AplikasiLP\n\n
Silahkan balas "YA" untuk mengaktifkan link!\n\n
------Pesan otomatis ini tidak terhubung dengan agen CS------', `list_tag_group_tag` = '{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{recipient_name},{shipper_name},{stt_number},{proof_photo}'
WHERE `list_tag_group_action` = 'POD (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hai {consignee_name}! Paketmu dari {shipper_name} nomor {stt_number} sudah diterima oleh {recipient_name}! Silahkan cek bukti penerima berikut {proof_photo}. Terima kasih sudah menggunakan Lion Parcel! Ditunggu kiriman selanjutnya, ya!\n\n
Cobain aplikasi Lion Parcel, dijamin Berani Diandelin karena punya gratis Pick Up & Parcel Poin berkali-kali lipat! Yuk, download aplikasinya sekarang di https://bit.ly/AplikasiLP\n\n
Silahkan balas "YA" untuk mengaktifkan link!\n\n
------Pesan otomatis ini tidak terhubung dengan agen CS------', `list_tag_group_tag` = '{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{recipient_name},{shipper_name},{stt_number},{proof_photo}'
WHERE `list_tag_group_action` = 'POD (Non COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hai Pelanggan Lion Parcel. Paket COD nomor {stt_number} kamu tertunda karena {undeliver_reason} {proof_photo}. Jangan khawatir, kami akan kirim lagi di hari kerja berikutnya. Pastikan kamu dapat dihubungi ya.\n\n
{paragraph_dex}\n\n
Silahkan balas "YA" untuk mengaktifkan link!\n\n
------Pesan otomatis ini tidak terhubung dengan agen CS------' , `list_tag_group_tag` = '{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{rebuttal_link},{undeliver_reason},{paragraph_dex},{proof_photo}'
WHERE `list_tag_group_action` = 'DEX (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hai Pelanggan Lion Parcel. Paket {stt_number} kamu tertunda karena {undeliver_reason} {proof_photo}. Jangan khawatir, kami akan kirim lagi di hari kerja berikutnya. Pastikan kamu dapat dihubungi ya.\n\n
{paragraph_dex}\n\n
Silahkan balas "YA" untuk mengaktifkan link!\n\n
------Pesan otomatis ini tidak terhubung dengan agen CS------', `list_tag_group_tag` = '{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{rebuttal_link},{undeliver_reason},{paragraph_dex},{proof_photo}'
WHERE `list_tag_group_action` = 'DEX (Non COD)';
