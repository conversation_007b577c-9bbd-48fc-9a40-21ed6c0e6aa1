CREATE TABLE IF NOT EXISTS `config_dfod_pasti_inactive_period` (
    `cdpip_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `cdpip_name` varchar(255) NOT NULL,
    `cdpip_start_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
    `cdpip_end_date` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
    `cdpip_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `cdpip_created_by` bigint(20) NOT NULL,
    `cdpip_updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `cdpip_updated_by` bigint(20) NOT NULL,
    PRIMARY KEY (`cdpip_id`),
    KEY `idx_cdpip_period` (`cdpip_start_date`, `cdpip_end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `config_dfod_pasti_exception` (
    `cdpe_id` int(10) NOT NULL AUTO_INCREMENT,
    `cdpe_product_type` varchar(255) NOT NULL,
    `cdpe_commodity_code` varchar(255) NOT NULL,
    `cdpe_origin_city_id` varchar(3) NOT NULL,
    `cdpe_origin_district_id` varchar(20) NOT NULL,
    `cdpe_destination_city_id` varchar(3) NOT NULL,
    `cdpe_destination_district_id` varchar(20) NOT NULL,
    `cdpe_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `cdpe_created_by` bigint(20) NOT NULL,
    `cdpe_updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `cdpe_updated_by` bigint(20) NOT NULL,
    `cdpe_reference_id` varchar(255) NOT NULL,
    PRIMARY KEY (`cdpe_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
