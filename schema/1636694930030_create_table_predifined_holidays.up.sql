CREATE TABLE IF NOT EXISTS `predefined_holiday` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(255) NULL DEFAULT 0,
  `description` TEXT NULL,
  `date` DATETIME NULL,
  `include_city` TEXT NULL,
  `created_at` TIMESTAMP NULL,
  `created_by` INT NULL,
  `updated_at` TIMESTAMP NULL,
  `updated_by` INT NULL,
  PRIMARY KEY (`id`),
  INDEX `idx_name` (`name` ASC),
  INDEX `idx_date` (`date` ASC),
  INDEX `idx_include_city` (`include_city`(100) ASC)
 ); 