-- INSERT bulk permission --
-- START INSERT -
INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_enable', 
             'to show bulk upload action menu on sidebar', 
              NOW(), 0) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_enable');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_list_view',
             'to be able to display bulk upload action history list',
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_list_view');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_create',
             'to show button create bulk upload to be able to create new bulk upload',
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_create');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_detail_view',
             'to view detail bulk upload',
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_detail_view');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_role',
             'to be able to choose role bulk upload in the tipe berkas field',
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_role');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_account',
             'to be able to choose account bulk upload in the tipe berkas field',
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_account');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_route_mapping',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_route_mapping');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_vendor',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_vendor');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_route_leg',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_route_leg');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_leg',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_leg');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_route',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_route');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_transport_vendor',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_transport_vendor');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_create_retail_base_rate',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_create_retail_base_rate');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_update_retail_base_rate',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_update_retail_base_rate');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_commodity_surcharge',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_commodity_surcharge');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_commodity',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_commodity');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_add_mapping_location_client_to_lionparcel',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'bulk_upload_type_add_mapping_location_client_to_lionparcel');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'bulk_upload_type_update_mapping_location_client_to_lionparcel',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'bulk_upload_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(
        SELECT name FROM permission WHERE name = 'bulk_upload_type_update_mapping_location_client_to_lionparcel');

-- INSERT create booking permission --
INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'shipment_create_booking_type_manual',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'booking_feature_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'shipment_create_booking_type_manual');

INSERT INTO permission (name, description, created_at, parent_id)
SELECT *
FROM (SELECT 'shipment_create_booking_type_shipment_id_booking_id',
             null,
             NOW(),
             (SELECT id FROM permission WHERE name = 'booking_feature_enable' LIMIT 1)) as tmp
WHERE NOT EXISTS(SELECT name FROM permission WHERE name = 'shipment_create_booking_type_shipment_id_booking_id');
-- END INSERT -

-- DELETE existing bulk permission --
-- START DELETE--
DELETE
FROM role_permission
WHERE permission_id IN (SELECT id
                        FROM permission
                        WHERE name IN ('bulk_upload_enable',
                                       'bulk_upload_list_view',
                                       'bulk_upload_create',
                                       'bulk_upload_detail_view',
                                       'bulk_upload_type_role',
                                       'bulk_upload_type_account')
                          AND account_role_id in (SELECT account_role_id
                                                  FROM account_role
                                                  WHERE account_role_type in
                                                        ('internal', 'console', 'sub-console', 'pos', 'client')));

DELETE
FROM role_permission
WHERE permission_id IN (SELECT id
                        FROM permission
                        WHERE name IN (
                                       'bulk_upload_type_route_mapping',
                                       'bulk_upload_type_vendor',
                                       'bulk_upload_type_route_leg',
                                       'bulk_upload_type_leg',
                                       'bulk_upload_type_route',
                                       'bulk_upload_type_transport_vendor',
                                       'bulk_upload_type_update_retail_base_rate',
                                       'bulk_upload_type_create_retail_base_rate',
                                       'bulk_upload_type_commodity_surcharge',
                                       'bulk_upload_type_commodity',
                                       'bulk_upload_type_add_mapping_location_client_to_lionparcel',
                                       'bulk_upload_type_update_mapping_location_client_to_lionparcel')
                          AND account_role_id in
                              (SELECT account_role_id FROM account_role WHERE account_role_type in ('internal')));

-- DELETE create booking permission --
DELETE
FROM role_permission
WHERE permission_id IN
      (SELECT id
       FROM permission
       WHERE name IN ('shipment_create_booking_type_manual', 'shipment_create_booking_type_shipment_id_booking_id')
         AND account_role_id in
             (SELECT account_role_id FROM account_role WHERE account_role_type in ('pos', 'client')));

DELETE
FROM role_permission
WHERE permission_id IN
      (SELECT id
       FROM permission
       WHERE name IN ('shipment_create_booking_type_shipment_id_booking_id')
         AND account_role_id in (SELECT account_role_id FROM account_role WHERE account_role_type in ('pos')));
-- END DELETE--

-- INSERT new role bulk permission --
-- START INSERT--
INSERT INTO role_permission (permission_id, account_role_id)
SELECT p.id, ar.account_role_id
FROM permission p
         CROSS JOIN account_role ar
WHERE p.name IN (
                 'bulk_upload_type_route_mapping',
                 'bulk_upload_type_vendor',
                 'bulk_upload_type_route_leg',
                 'bulk_upload_type_leg',
                 'bulk_upload_type_route',
                 'bulk_upload_type_transport_vendor',
                 'bulk_upload_type_update_retail_base_rate',
                 'bulk_upload_type_create_retail_base_rate',
                 'bulk_upload_type_commodity_surcharge',
                 'bulk_upload_type_commodity',
                 'bulk_upload_type_add_mapping_location_client_to_lionparcel',
                 'bulk_upload_type_update_mapping_location_client_to_lionparcel')
  AND ar.account_role_type IN
      ('internal');

INSERT INTO role_permission (permission_id, account_role_id)
SELECT p.id, ar.account_role_id
FROM permission p
         CROSS JOIN account_role ar
WHERE p.name IN (
                 'bulk_upload_enable',
                 'bulk_upload_list_view',
                 'bulk_upload_create',
                 'bulk_upload_detail_view',
                 'bulk_upload_type_role',
                 'bulk_upload_type_account')
  AND ar.account_role_type IN
      ('internal', 'console', 'sub-console', 'pos', 'client');

-- INSERT new role create booking permission --
INSERT INTO role_permission (permission_id, account_role_id)
SELECT p.id, ar.account_role_id
FROM permission p
         CROSS JOIN account_role ar
WHERE p.name IN (
                 'shipment_create_booking_type_manual',
                 'shipment_create_booking_type_shipment_id_booking_id')
  AND ar.account_role_type IN
      ('pos');

INSERT INTO role_permission (permission_id, account_role_id)
SELECT p.id, ar.account_role_id
FROM permission p
         CROSS JOIN account_role ar
WHERE p.name IN (
    'shipment_create_booking_type_manual')
  AND ar.account_role_type IN
      ('client');
-- END INSERT--
