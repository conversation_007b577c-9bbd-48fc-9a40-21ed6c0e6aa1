INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_commission_config_booking', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'commission_config_booking_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_commission_config_booking');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'pricing_configurable_price_detail_edit_hw_surcharge', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'pricing_configurable_price_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'pricing_configurable_price_detail_edit_hw_surcharge');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'pricing_configurable_price_detail_edit_adjustment_penalty', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'pricing_configurable_price_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'pricing_configurable_price_detail_edit_adjustment_penalty');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'pricing_configurable_price_detail_edit_insurance', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'pricing_configurable_price_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'pricing_configurable_price_detail_edit_insurance');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'pricing_configurable_price_detail_edit_woodpacking', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'pricing_configurable_price_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'pricing_configurable_price_detail_edit_woodpacking');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'pricing_configurable_price_detail_edit_refund_config', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'pricing_configurable_price_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'pricing_configurable_price_detail_edit_refund_config');