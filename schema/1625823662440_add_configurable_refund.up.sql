INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, 
        refund_amount, refund_quantifier, refund_amount_insurance, refund_origin_city_exclude) 
        
        VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 
                'STT ADJUSTED (BEFORE PUP)', 
                (SELECT * FROM(select cr.refund_amount from configurable_refund cr where cr.refund_stt_last_status = 'BKD') tblCR),
                (SELECT * FROM(select cr.refund_quantifier from configurable_refund cr where cr.refund_stt_last_status = 'BKD') tblCR), 
                (SELECT * FROM(select cr.refund_amount_insurance from configurable_refund cr where cr.refund_stt_last_status = 'BKD') tblCR), 
                (SELECT * FROM(select cr.refund_origin_city_exclude from configurable_refund cr where cr.refund_stt_last_status = 'BKD') tblCR)
            )