CREATE TABLE IF NOT EXISTS `event` (
    `event_id` varchar(150) NOT NULL,
    `event_type` varchar(100) NOT NULL DEFAULT '',
    `event_data` LONGTEXT NOT NULL,
    `event_aggregate_id` varchar(150) NOT NULL,
    `event_aggregate_type` varchar(100) NOT NULL,
    `event_status` varchar(100) NOT NULL DEFAULT '',
    `event_retry` int(11) NOT NULL DEFAULT 0,
    `event_is_last_data` tinyint(1) NOT NULL DEFAULT '0',
    `event_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `event_updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`event_id`)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE INDEX event_type_IDX USING BTREE ON `event` (event_type);
CREATE INDEX event_aggregate_id_IDX USING BTREE ON `event` (event_aggregate_id);
CREATE INDEX event_aggregate_type_IDX USING BTREE ON `event` (event_aggregate_type);
CREATE INDEX event_status_IDX USING BTREE ON `event` (event_status);
CREATE INDEX event_retry_IDX USING BTREE ON `event` (event_retry);