CREATE TABLE `client_district_rate` (
    `client_district_rate_id` int unsigned NOT NULL AUTO_INCREMENT,
    `client_district_rate_origin_id` varchar(100) DEFAULT NULL,
    `client_district_rate_destination_id` varchar(100) DEFAULT NULL,
    `client_district_rate_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `client_district_rate_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
    `client_district_rate_created_by` int unsigned NOT NULL DEFAULT '0',
    `client_district_rate_updated_by` int unsigned NOT NULL DEFAULT '0',
    `client_district_rate_version` varchar(250) NOT NULL,
    `client_district_rate_service_type` enum('package','document') NOT NULL DEFAULT 'package',
    `client_district_rate_product_type` varchar(50) NOT NULL DEFAULT '',
    `client_district_rate_start_date` timestamp NULL DEFAULT NULL,
    `client_district_rate_end_date` timestamp NULL DEFAULT NULL,
    `eligible_archived_type` tinyint(1) NOT NULL DEFAULT '0',
    PRIMARY KEY (`client_district_rate_id`,`eligible_archived_type`),
    KEY `idx_client_district_rate_origin_id` (`client_district_rate_origin_id`),
    KEY `idx_client_district_rate_destination_id` (`client_district_rate_destination_id`),
    KEY `idx_client_district_rate_version` (`client_district_rate_version`) USING BTREE,
    KEY `idx_district_rate_product_type` (`client_district_rate_product_type`),
    KEY `idx_client_district_rate_service_type` (`client_district_rate_service_type`),
    KEY `idx_client_district_rate_start_date` (`client_district_rate_start_date`),
    KEY `idx_client_district_rate_end_date` (`client_district_rate_end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
/*!50500 PARTITION BY LIST  COLUMNS(eligible_archived_type)
(PARTITION ready_archive VALUES IN (2) ENGINE = InnoDB,
PARTITION eligible_archive VALUES IN (1) ENGINE = InnoDB,
PARTITION ongoing VALUES IN (0) ENGINE = InnoDB) */;
