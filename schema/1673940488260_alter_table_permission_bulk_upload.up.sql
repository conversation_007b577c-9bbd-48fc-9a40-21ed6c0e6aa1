INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_add_mapping_location_district', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_add_mapping_location_district');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_update_mapping_location_district', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_update_mapping_location_district');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_add_embargo', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_add_embargo');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_update_embargo', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_update_embargo');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_add_country', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_add_country');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_update_country', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_update_country');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_add_city', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_add_city');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_update_city', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_update_city');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_add_district', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_add_district');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_update_district', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_update_district');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_update_ursa', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_update_ursa');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_update_district_vendor', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_update_district_vendor');