CREATE TABLE `commodity_shc` (
  `commodity_shc_id` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL,
  `commodity_shc_code` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL,
  `commodity_shc_description` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL,
  `commodity_shc_status` varchar(150) COLLATE utf8mb4_unicode_ci NOT NULL,
  `commodity_shc_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `commodity_shc_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
PRIMARY KEY (`commodity_shc_id`),
  KEY `commodity_shc_commodity_shc_code_IDX` (`commodity_shc_code`),
  KEY `commodity_shc_commodity_shc_status_IDX` (`commodity_shc_status`),
  KEY `commodity_shc_commodity_shc_created_at_IDX` (`commodity_shc_created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;