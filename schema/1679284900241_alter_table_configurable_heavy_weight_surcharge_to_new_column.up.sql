ALTER TABLE `configurable_heavy_weight_surcharge` ADD `heavy_weight_surcharge_product_type_include` TEXT NOT NULL AFTER `heavy_weight_surcharge_product_type_exclude`;
ALTER TABLE `configurable_heavy_weight_surcharge` ADD `heavy_weight_surcharge_type` VARCHAR(100) NOT NULL;
ALTER TABLE `configurable_heavy_weight_surcharge` ADD `heavy_weight_surcharge_status` VARCHAR(100) NOT NULL;
ALTER TABLE `configurable_heavy_weight_surcharge` ADD `heavy_weight_surcharge_description` TEXT NOT NULL;
ALTER TABLE `configurable_heavy_weight_surcharge` ADD `heavy_weight_surcharge_updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE `configurable_heavy_weight_surcharge` ADD `heavy_weight_surcharge_updated_by` INT(11) DEFAULT 0;
