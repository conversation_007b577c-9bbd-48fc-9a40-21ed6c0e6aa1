CREATE TABLE `configurable_top_up_fee` (
  `ctuf_id` varchar(36) NOT NULL,
  `ctuf_account_type` varchar(30) NOT NULL,
  `ctuf_top_up_fee` int NOT NULL DEFAULT 0,
  `ctuf_is_all_pos` boolean NOT NULL,
  `ctuf_status` varchar(20) NOT NULL,
  `ctuf_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ctuf_created_by` bigint(11) unsigned NOT NULL,
  `ctuf_updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ctuf_updated_by` bigint(11) unsigned NOT NULL,
  PRIMARY KEY (`ctuf_id`),
  KEY `idx_ctuf_updated_by` (`ctuf_updated_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;