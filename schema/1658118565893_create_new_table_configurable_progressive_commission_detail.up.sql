CREATE TABLE configurable_progressive_commission_detail (
	cpcd_id INT auto_increment NOT NULL,
	cpcd_milestone_id INT NOT NULL,
	cpcd_milestone_name varchar(100) DEFAULT '' NULL,
	cpcd_milestone_limit DOUBLE DEFAULT 0 NOT NULL,
	cpcd_milestone_reference TEXT NULL,
	cpcd_commission_additional DOUBLE DEFAULT 0 NOT NULL,
	cpcd_cpc_id INT NOT NULL,
	CONSTRAINT configurable_progressive_commission_detail_PK PRIMARY KEY (cpcd_id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8
COLLATE=utf8_general_ci;
CREATE INDEX configurable_progressive_commission_detail_cpcd_cpc_id_IDX USING BTREE ON configurable_progressive_commission_detail (cpcd_cpc_id);
