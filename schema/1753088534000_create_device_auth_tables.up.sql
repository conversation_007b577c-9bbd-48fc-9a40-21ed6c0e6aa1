CREATE TABLE IF NOT EXISTS `device_auth` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `partner_id` bigint(20) NOT NULL,
  `partner_type` varchar(50) NOT NULL DEFAULT '',
  `hub_id` bigint(20) NOT NULL,
  `account_id` bigint(20) NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `username` varchar(150) NOT NULL DEFAULT '',
  `fcm_token` text,
  `device_name` varchar(255) NOT NULL DEFAULT '',
  `device_type` varchar(50) NOT NULL DEFAULT 'mobile',
  `device_os` varchar(100) NOT NULL DEFAULT '',
  `location` varchar(255) NOT NULL DEFAULT '',
  `lat_long` varchar(100) NOT NULL DEFAULT '',
  `user_agent` text,
  `ip_address` varchar(45) NOT NULL DEFAULT '',
  `device_master` tinyint(1) NOT NULL DEFAULT 0,
  `last_active_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_auth_account_id` (`account_id`),
  KEY `idx_device_auth_partner_id` (`partner_id`),
  KEY `idx_device_auth_hub_id` (`hub_id`),
  KEY `idx_device_auth_device_master` (`device_master`),
  KEY `idx_device_auth_account_device` (`account_id`, `device_id`),
  KEY `idx_device_auth_master_account` (`device_master`, `account_id`),
  KEY `idx_device_auth_master_partner` (`device_master`, `partner_id`),
  KEY `idx_device_auth_master_hub` (`device_master`, `hub_id`),
  KEY `idx_device_auth_last_active` (`last_active_at`),
  KEY `idx_device_auth_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `device_approval` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `partner_id` bigint(20) NOT NULL,
  `partner_type` varchar(50) NOT NULL DEFAULT '',
  `hub_id` bigint(20) NOT NULL,
  `account_id` bigint(20) NOT NULL,
  `by_device_id` varchar(255) NOT NULL DEFAULT '',
  `device_id` varchar(255) NOT NULL,
  `device_name` varchar(255) NOT NULL DEFAULT '',
  `device_type` varchar(50) NOT NULL DEFAULT 'mobile',
  `device_os` varchar(100) NOT NULL DEFAULT '',
  `approval_status` varchar(50) NOT NULL DEFAULT 'pending_approval',
  `location` varchar(255) NOT NULL DEFAULT '',
  `lat_long` varchar(100) NOT NULL DEFAULT '',
  `user_agent` text,
  `ip_address` varchar(45) NOT NULL DEFAULT '',
  `fcm_token` text,
  `device_master` tinyint(1) NOT NULL DEFAULT 0,
  `last_active_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `approved_at` timestamp NULL DEFAULT NULL,
  `rejected_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_device_approval_account_id` (`account_id`),
  KEY `idx_device_approval_device_id` (`device_id`),
  KEY `idx_device_approval_partner_id` (`partner_id`),
  KEY `idx_device_approval_hub_id` (`hub_id`),
  KEY `idx_device_approval_by_device_id` (`by_device_id`),
  KEY `idx_device_approval_status` (`approval_status`),
  KEY `idx_device_approval_device_partner_hub` (`device_id`, `partner_id`, `hub_id`),
  KEY `idx_device_approval_account_device` (`account_id`, `device_id`),
  KEY `idx_device_approval_created_at` (`created_at`),
  KEY `idx_device_approval_expires_at` (`expires_at`),
  KEY `idx_device_approval_approved_at` (`approved_at`),
  KEY `idx_device_approval_rejected_at` (`rejected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `device_auth_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `reference_id` bigint(20) NOT NULL,
  `reference_type` varchar(50) NOT NULL DEFAULT 'device_approval',
  `account_id` bigint(20) NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `action` varchar(50) NOT NULL,
  `previous_status` varchar(50) NOT NULL,
  `new_status` varchar(50) NOT NULL,
  `performed_by` varchar(150) NOT NULL DEFAULT '',
  `performed_by_user_id` bigint(20) NOT NULL,
  `reason` text,
  `ip_address` varchar(45) NOT NULL DEFAULT '',
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_device_auth_log_reference_id` (`reference_id`),
  KEY `idx_device_auth_log_reference_type` (`reference_type`),
  KEY `idx_device_auth_log_account_id` (`account_id`),
  KEY `idx_device_auth_log_device_id` (`device_id`),
  KEY `idx_device_auth_log_performed_by_user_id` (`performed_by_user_id`),
  KEY `idx_device_auth_log_created_at` (`created_at`),
  KEY `idx_device_auth_log_action` (`action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
