CREATE TABLE `dex_fake_dex_configuration` (
  `dfdc_id` varchar(36) NOT NULL,
  `dfdc_name` varchar(255) NOT NULL,
  `dfdc_description` varchar(255) NOT NULL,
  `dfdc_type` varchar(100) NOT NULL,
  `dfdc_reason_code` varchar(50) DEFAULT NULL,
  `dfdc_reason_category` varchar(100) DEFAULT NULL,
  `dfdc_param_start_time` varchar(8) DEFAULT NULL,
  `dfdc_param_end_time` varchar(8) DEFAULT NULL,
  `dfdc_param_exclude_weekend_holiday` tinyint(1) Not NULL DEFAULT 0,
  `dfdc_office_start_time` varchar(8) DEFAULT NULL,
  `dfdc_office_end_time` varchar(8) DEFAULT NULL,
  `dfdc_office_exclude_weekend_holiday` tinyint(1) Not NULL DEFAULT 0,
  `dfdc_genesis` tinyint(1) Not NULL DEFAULT 0,
  `dfdc_driver_app` tinyint(1) Not NULL DEFAULT 0,
  `dfdc_status` varchar(20)  DEFAULT "",
  `dfdc_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dfdc_created_by` int(11) Not NULL DEFAULT 0,
  `dfdc_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `dfdc_updated_by` int(11) NULL DEFAULT null,
  PRIMARY KEY (`dfdc_id`),
  KEY `dfdc_type_IDX` (`dfdc_type`),
  KEY `dfdc_status_IDX` (`dfdc_status`),
  KEY `dfdc_created_at_IDX` (`dfdc_created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
