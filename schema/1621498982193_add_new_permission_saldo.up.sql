-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'saldo_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'saldo_enable');
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'saldo_withdraw_view_detail', NOW(), (SELECT id FROM permission WHERE name = 'saldo_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'saldo_withdraw_view_detail');
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'saldo_withdraw_request_detail', NOW(), (SELECT id FROM permission WHERE name = 'saldo_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'saldo_withdraw_request_detail');
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'saldo_beneficiary_account_view_detail', NOW(), (SELECT id FROM permission WHERE name = 'saldo_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'saldo_beneficiary_account_view_detail');
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'saldo_beneficiary_account_add_detail', NOW(), (SELECT id FROM permission WHERE name = 'saldo_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'saldo_beneficiary_account_add_detail');
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'saldo_beneficiary_account_edit_detail', NOW(), (SELECT id FROM permission WHERE name = 'saldo_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'saldo_beneficiary_account_edit_detail');


-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('saldo_enable','saldo_withdraw_view_detail','saldo_withdraw_request_detail','saldo_beneficiary_account_view_detail','saldo_beneficiary_account_add_detail','saldo_beneficiary_account_edit_detail')
	AND ar.account_role_type IN ('pos','client');


