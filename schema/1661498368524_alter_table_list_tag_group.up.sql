UPDATE `list_tag_group` SET `list_tag_group_tag` = '{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{rebuttal_link},{undeliver_reason},{paragraph_dex}', `list_tag_group_content` = 'Hai Pelanggan Lion Parcel. Paket {stt_number} kamu mengalami gagal antar karena {undeliver_reason}. Kami akan coba kirim kembali maksimal 2 kali. Pastikan kamu dapat dihubungi oleh kurir ya! \r\n\r\n\r\n\r\n{paragraph_dex}\r\n\r\n\r\n\r\nSilahkan balas \"YA\" untuk mengaktifkan link!\r\n\r\n\r\n\r\n------Pesan otomatis ini tidak terhubung dengan agen CS------' WHERE `list_tag_group`.`list_tag_group_type_cod` = "non_cod" and `list_tag_group`.`list_tag_group_status_stt` = "DEX";
UPDATE `list_tag_group` SET `list_tag_group_tag` = '{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{rebuttal_link},{undeliver_reason},{paragraph_dex}', `list_tag_group_content` = 'Hai Pelanggan Lion Parcel. Paket COD nomor {stt_number} kamu mengalami gagal antar karena {undeliver_reason}. Kami akan coba kirim kembali maksimal 2 kali. Pastikan kamu dapat dihubungi oleh kurir ya!\r\n\r\n\r\n\r\n{paragraph_dex}\r\n\r\n\r\n\r\n\r\n\r\nSilahkan balas \"YA\" untuk mengaktifkan link!\r\n\r\n\r\n\r\n------Pesan otomatis ini tidak terhubung dengan agen CS------' WHERE `list_tag_group`.`list_tag_group_type_cod` = "cod" and `list_tag_group`.`list_tag_group_status_stt` = "DEX";