-- horde.client_district_rate_detail definition

CREATE TABLE `client_district_rate_detail` (
                                               `client_district_rate_detail_id` int unsigned NOT NULL AUTO_INCREMENT,
                                               `client_district_rate_detail_type` varchar(25) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
                                               `client_district_rate_detail_range_type` enum('chargeable_weight','volume_weight','gr_weight') DEFAULT NULL,
                                               `client_district_rate_detail_start` float unsigned NOT NULL DEFAULT '0',
                                               `client_district_rate_detail_end` float unsigned NOT NULL,
                                               `client_district_rate_detail_rate` double NOT NULL,
                                               `client_district_rate_id` int unsigned NOT NULL,
                                               `client_district_rate_detail_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                               `client_district_rate_detail_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
                                               `eligible_archived_type` tinyint(1) NOT NULL DEFAULT '0',
                                               PRIMARY KEY (`client_district_rate_detail_id`,`eligible_archived_type`),
                                               KEY `idx_client_district_rate_id` (`client_district_rate_id`),
                                               KEY `idx_client_district_rate_detail_type` (`client_district_rate_detail_type`),
                                               KEY `idx_client_district_rate_detail_range_type` (`client_district_rate_detail_range_type`),
                                               KEY `idx_client_district_rate_detail_start` (`client_district_rate_detail_start`),
                                               KEY `idx_client_district_rate_detail_end` (`client_district_rate_detail_end`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3
/*!50500 PARTITION BY LIST  COLUMNS(eligible_archived_type)
(PARTITION ready_archive VALUES IN (2) ENGINE = InnoDB,
 PARTITION eligible_archive VALUES IN (1) ENGINE = InnoDB,
 PARTITION ongoing VALUES IN (0) ENGINE = InnoDB) */;