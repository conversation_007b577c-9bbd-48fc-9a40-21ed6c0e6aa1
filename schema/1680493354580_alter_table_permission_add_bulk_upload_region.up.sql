INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_upsert_region_city', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_upsert_region_city');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bulk_upload_upsert_region', '', NOW(), (select id from permission where name = 'bulk_upload_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bulk_upload_upsert_region');