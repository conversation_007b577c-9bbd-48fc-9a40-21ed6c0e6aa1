UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {COD_amount} dengan No. STT {stt_number} sedang dalam proses pengiriman. Cek status paket Anda di http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'BKD (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {COD_amount} dengan No. STT {stt_number} telah diterima oleh {recipient_name} pada {datetime}.\n
\n
Jika paket belum Anda terima atau paket rusak mohon hubungi CS kami di 02180820072 atau Klaim paket pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'POD (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {COD_amount} dengan No. STT {stt_number} tidak terkirim karena {undelivered_reason} pada {datetime}. Hubungi Customer Service kami untuk informasi lebih lanjut di 0218082007. \n
\n
Cek status paket Anda di http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'DEX (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {COD_amount} dengan No. STT {stt_number} sedang dalam pengantaran ke alamat Anda oleh {driver_name} {driver_phone_number} pada {datetime}. Mohon siapkan biaya COD sesuai dengan paket pesanan Anda. \n
\n
Cek status paket Anda di http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'DEL (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {COD_amount} dengan No. STT {stt_number} telah kami kembalikan kepada pengirim {shipper_name} karena {CODREJ_reason} pada {datetime}. Hubungi Customer Service kami untuk informasi lebih lanjut di 0218082007. \n
\n
Cek status paket Anda di http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'CODREJ (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket Anda dengan No. STT {stt_number} sedang dalam proses pengiriman. Cek status paket Anda pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'BKD (Non COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_number},\n
Paket Anda dengan No. STT {STT Number} telah diterima oleh {recipient_name} pada {datetime}.\n
\n
Jika paket belum Anda terima atau paket rusak mohon hubungi CS kami di 02180820072 atau Klaim paket pada Apps Lion Parcel http://bit.ly/LioApps'
WHERE `list_tag_group_action` = 'POD (Non COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_number},\n
Paket Anda dengan No. STT {stt_number} tidak terkirim karena {undelivered_reason} pada {datetime}. Hubungi Customer Service kami untuk informasi lebih lanjut di 0218082007. \n
\n
Cek status paket Anda di http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'DEX (Non COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket Anda dengan No. STT {stt_number} dari pengirim {shipper_name} sedang dalam pengantaran ke alamat Anda oleh {driver_name} {driver_phone_number} pada {datetime}. \n
\n
Cek status paket Anda di http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'DEL (Non COD)';