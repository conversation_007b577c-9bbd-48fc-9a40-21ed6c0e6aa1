CREATE TABLE `cargo_configuration` (
  `cc_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cc_origin` varchar(10) NOT NULL DEFAULT '',
  `cc_origin_name` varchar(100) NOT NULL DEFAULT '',
  `cc_destination` varchar(10) NOT NULL DEFAULT '',
  `cc_destination_name` varchar(100) NOT NULL DEFAULT '',
  `cc_mode_transport` varchar(20) NOT NULL DEFAULT '',
  `cc_comodities_code` text NOT NULL,
  `cc_product_type` text NOT NULL,
  `cc_maximum_weight` float unsigned NOT NULL DEFAULT '0',
  `cc_maximum_volume` float unsigned NOT NULL DEFAULT '0',
  `cc_vendor_code` varchar(100) NOT NULL DEFAULT '',
  `cc_vendor_name` varchar(100) NOT NULL DEFAULT '',
  `cc_flight` LONGTEXT NOT NULL,
  `cc_status` enum('active','inactive') NOT NULL DEFAULT 'inactive',
  `cc_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `cc_created_by` int(11) DEFAULT 0,
  `cc_created_name` varchar(100) DEFAULT '',
  `cc_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cc_updated_by` int(11) DEFAULT 0,
  `cc_updated_name` varchar(100) DEFAULT '',
   PRIMARY KEY (`cc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
CREATE INDEX cc_id_IDX USING BTREE ON cargo_configuration (cc_id);
CREATE INDEX cc_origin_IDX USING BTREE ON cargo_configuration (cc_origin);
CREATE INDEX cc_destination_IDX USING BTREE ON cargo_configuration (cc_destination);
CREATE INDEX cc_status_IDX USING BTREE ON cargo_configuration (cc_status);
CREATE INDEX cc_mode_transport_IDX USING BTREE ON cargo_configuration (cc_mode_transport);
CREATE INDEX cc_created_at_IDX USING BTREE ON cargo_configuration (cc_created_at);
CREATE INDEX cc_vendor_code_IDX USING BTREE ON cargo_configuration (cc_vendor_code);