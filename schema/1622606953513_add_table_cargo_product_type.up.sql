CREATE TABLE IF NOT EXISTS `cargo_product_type` (
  `cargo_product_type_id` INT NOT NULL,
  `cargo_product_type_code` VARCHAR(50) NULL,
  `cargo_product_type_description` TEXT NULL,
  `cargo_product_type_status` ENUM('active', 'inactive') NOT NULL DEFAULT 'inactive',
  `cargo_product_type_updated_by` INT NULL,
  `cargo_product_type_created_by` INT NULL,
  `cargo_product_type_created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `cargo_product_type_updated_at` TIMESTAMP NULL,
  PRIMARY KEY (`cargo_product_type_id`),
  INDEX `idx_cargo_product_type_code` (`cargo_product_type_code` ASC),
  INDEX `idx_cargo_product_type_status` (`cargo_product_type_status` ASC))
ENGINE = InnoDB;

ALTER TABLE product_type ADD COLUMN product_type_cargo_product_type_code VARCHAR(50) NULL AFTER product_type_status;