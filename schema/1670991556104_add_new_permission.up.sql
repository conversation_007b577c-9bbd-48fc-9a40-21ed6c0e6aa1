INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'ready_to_cargo_enable', '', NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'ready_to_cargo_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'ready_to_cargo_list_view', '', NOW(), (select id from permission where name = 'ready_to_cargo_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'ready_to_cargo_list_view');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'ready_to_cargo_detail_view', '', NOW(), (select id from permission where name = 'ready_to_cargo_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'ready_to_cargo_detail_view');
