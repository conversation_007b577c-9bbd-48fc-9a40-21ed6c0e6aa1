-- PUP

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'topup_manual_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_manual_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'topup_manual_history_view_list', NOW(), (SELECT id FROM permission WHERE name = 'topup_manual_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_manual_history_view_list');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'topup_manual_request', NOW(), (SELECT id FROM permission WHERE name = 'topup_manual_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'topup_manual_request');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
    p.name IN ('topup_manual_enable','topup_manual_history_view_list','topup_manual_request')
	AND ar.account_role_type IN ('pos');