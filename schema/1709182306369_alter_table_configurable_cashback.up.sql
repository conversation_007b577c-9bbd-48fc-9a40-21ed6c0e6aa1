
ALTER TABLE configurable_cashback ADD INDEX idx_ccb_id (ccb_id);
ALTER TABLE configurable_cashback ADD INDEX idx_ccb_type (ccb_type);
ALTER TABLE configurable_cashback ADD INDEX idx_ccb_account_type (ccb_account_type);
ALTER TABLE configurable_cashback ADD INDEX idx_ccb_start_date (ccb_start_date);
ALTER TABLE configurable_cashback ADD INDEX idx_ccb_end_date (ccb_end_date);
ALTER TABLE configurable_cashback ADD INDEX idx_ccb_status (ccb_status);
ALTER TABLE configurable_cashback ADD INDEX idx_ccb_created_at (ccb_created_at);
ALTER TABLE configurable_cashback ADD INDEX idx_ccb_updated_at (ccb_updated_at);

