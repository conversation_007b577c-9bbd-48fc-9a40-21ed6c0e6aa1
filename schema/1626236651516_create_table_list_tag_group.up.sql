CREATE TABLE `list_tag_group` (
  `list_tag_group_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `list_tag_group_action` varchar(250) NOT NULL DEFAULT '',
  `list_tag_group_tag` varchar(250) NOT NULL DEFAULT '',
  `list_tag_group_type_cod` varchar(250) NOT NULL DEFAULT '',
  `list_tag_group_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `list_tag_group_created_by` int(11) unsigned DEFAULT NULL,
  `list_tag_group_updated_at` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `list_tag_group_updated_by` int(11) unsigned DEFAULT NULL,
  PRIMARY KEY (`list_tag_group_id`),
  KEY `idx_list_tag_group_action` (`list_tag_group_action`),
  KEY `idx_list_tag_group_tag` (`list_tag_group_tag`),
  <PERSON><PERSON><PERSON> `idx_list_tag_group_type_cod` (`list_tag_group_type_cod`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO list_tag_group VALUES 
(1,'BKD (COD)','{cod_amount},{consignee_name},{datetime},{delivery_sla},{shipper_name},{stt_number}','cod','2021-07-14 14:39:20',1,NULL,NULL),
(2,'POD (COD)','{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{recipient_name},{shipper_name},{stt_number}','cod','2021-07-14 14:39:20',1,NULL,NULL),
(3,'DEX (COD)','{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{undelivered_reason}','cod','2021-07-14 14:39:20',1,'2021-07-19 12:00:00',NULL),
(4,'DEL (COD)','{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number}','cod','2021-07-14 14:39:20',1,'2021-07-19 12:00:00',NULL),
(5,'CODREJ (COD)','{cod_amount},{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{codrej_reason}','cod','2021-07-14 14:39:20',1,'2021-07-19 12:00:00',NULL),
(6,'BKD (Non COD)','{consignee_name},{datetime},{delivery_sla},{shipper_name},{stt_number}','non_cod','2021-07-14 14:39:20',1,'2021-07-19 12:00:00',NULL),
(7,'POD (Non COD)','{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{recipient_name},{shipper_name},{stt_number}','non_cod','2021-07-14 14:39:20',1,'2021-07-19 12:00:00',NULL),
(8,'DEX (Non COD)','{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number},{undelivered_reason}','non_cod','2021-07-14 14:39:20',1,'2021-07-19 12:00:00',NULL),
(9,'DEL (Non COD)','{consignee_name},{datetime},{delivery_sla},{driver_name},{driver_phone_number},{shipper_name},{stt_number}','non_cod','2021-07-14 14:39:20',1,'2021-07-19 12:00:00',NULL);