CREATE TABLE `configurable_cod` (
  `ccod_id` INT(11) NOT NULL AUTO_INCREMENT,
  `ccod_cpid` INT(11) NOT NULL,
  `ccod_name`varchar(100) NOT NULL DEFAULT '',
  `ccod_type_stt` ENUM('retail', 'client') NOT NULL DEFAULT 'retail',
  `ccod_type_calculation` ENUM('percentage', 'amount') NOT NULL DEFAULT 'percentage',
  `ccod_cod_type` ENUM('COD', 'PAD') NOT NULL DEFAULT 'COD',
  `ccod_references` ENUM('Publish Rate', 'Goods Amount') NOT NULL DEFAULT 'Publish Rate',
  `ccod_fee` DOUBLE NOT NULL DEFAULT '0',
  `ccod_created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `ccod_created_by` INT NULL,
  `ccod_updated_at` TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `ccod_updated_by` INT NULL,
  <PERSON>IMAR<PERSON>EY (`ccod_id`),
  KEY idx_ccod_cpid (ccod_cpid),
  KEY idx_ccod_type_stt (ccod_type_stt),
  KEY idx_ccod_cod_type (ccod_cod_type),
  KEY idx_ccod_references (ccod_references)
);
