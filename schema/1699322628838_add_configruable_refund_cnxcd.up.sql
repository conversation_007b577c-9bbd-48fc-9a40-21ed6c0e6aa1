INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount, refund_quantifier, refund_amount_insurance, refund_origin_city_exclude, refund_penalty)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'HALCD', 70, 'base_rate,shipping_surcharge', 100, 'no_city', 30)
ON DUPLICATE KEY UPDATE
    refund_configurable_price_id = (select configurable_price_id from configurable_price where configurable_price_type = 'refund'),
    refund_stt_last_status = 'HALCD',
    refund_amount = 70,
    refund_quantifier = 'base_rate,shipping_surcharge',
    refund_amount_insurance = 100,
    refund_origin_city_exclude = 'no_city',
    refund_penalty = 30; 