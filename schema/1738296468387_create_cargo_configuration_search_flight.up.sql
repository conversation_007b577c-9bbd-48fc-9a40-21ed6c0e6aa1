CREATE TABLE `cargo_configuration_search_flight` (
    `ccsf_id` int unsigned NOT NULL AUTO_INCREMENT,
    `ccsf_connection_type` varchar(20) NOT NULL DEFAULT '',
    `ccsf_product_name` varchar(255) NOT NULL DEFAULT '',
    `ccsf_airline_code` varchar(20) NOT NULL DEFAULT '',
    `ccsf_airport_code` varchar(20) NOT NULL DEFAULT '',
    `ccsf_is_route_setting` tinyint NOT NULL DEFAULT '0',
    `ccsf_connection_time` int unsigned NOT NULL DEFAULT 0,
    `ccsf_based_on` varchar(255) NOT NULL DEFAULT '',
    `ccsf_is_active` tinyint NOT NULL DEFAULT '0',
    PRIMARY KEY (`ccsf_id`),
    KEY `idx_ccsf_con_type` (`ccsf_connection_type`),
    KEY `idx_ccsf_con_type_air_code` (`ccsf_connection_type`,`ccsf_airport_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;