
CREATE TABLE IF NOT EXISTS configurable_cashback(
    ccb_id VARCHAR(36) NOT NULL PRIMARY KEY,
    ccb_type VARCHAR(30) NOT NULL,
    ccb_account_type VARCHAR(30) NOT NULL,
    ccb_is_all_pos BOOLEAN NOT NULL,
    ccb_start_date TIMESTAMP NOT NULL,
    ccb_end_date TIMESTAMP NOT NULL,
    ccb_tiers TEXT NOT NULL,
    ccb_status VARCHAR(20) NOT NULL,
    ccb_created_at TIMESTAMP NOT NULL,
    ccb_created_by INT UNSIGNED NOT NULL,
    ccb_updated_at TIMESTAMP NOT NULL,
    ccb_updated_by INT UNSIGNED NOT NULL
);