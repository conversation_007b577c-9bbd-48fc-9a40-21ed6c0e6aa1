INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_dest_sc_enable', NULL, NOW(), 0);
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_dest_sc_list_view', NULL, NOW(), (SELECT p.id FROM permission p WHERE p.`name` = "sti_dest_sc_enable"));
INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
VALUES ('sti_dest_sc_create', NULL, NOW(), (SELECT p.id FROM permission p WHERE p.`name` = "sti_dest_sc_enable"));

INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "sti_dest_sc_enable"), 3);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "sti_dest_sc_list_view"), 3);
INSERT INTO `role_permission`(`permission_id`, `account_role_id`) 
VALUES ((SELECT id FROM permission WHERE `name` = "sti_dest_sc_create"), 3);
