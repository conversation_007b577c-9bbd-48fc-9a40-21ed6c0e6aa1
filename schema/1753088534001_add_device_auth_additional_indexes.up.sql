# Additional Indexes for Device Authentication Tables
# This migration adds additional composite indexes for better query performance

# Additional indexes for device_auth table - with IF NOT EXISTS check
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_auth' AND INDEX_NAME = 'idx_device_auth_partner_hub_account');
SET @sql = IF(@index_exists = 0, 'ALTER TABLE `device_auth` ADD INDEX `idx_device_auth_partner_hub_account` (`partner_id`, `hub_id`, `account_id`)', 'SELECT "Index idx_device_auth_partner_hub_account already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_auth' AND INDEX_NAME = 'idx_device_auth_partner_type_account');
SET @sql = IF(@index_exists = 0, 'ALTER TABLE `device_auth` ADD INDEX `idx_device_auth_partner_type_account` (`partner_type`, `account_id`)', 'SELECT "Index idx_device_auth_partner_type_account already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_auth' AND INDEX_NAME = 'idx_device_auth_device_type');
SET @sql = IF(@index_exists = 0, 'ALTER TABLE `device_auth` ADD INDEX `idx_device_auth_device_type` (`device_type`)', 'SELECT "Index idx_device_auth_device_type already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

# Additional indexes for device_approval table  
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_approval' AND INDEX_NAME = 'idx_device_approval_status_created');
SET @sql = IF(@index_exists = 0, 'ALTER TABLE `device_approval` ADD INDEX `idx_device_approval_status_created` (`approval_status`, `created_at`)', 'SELECT "Index idx_device_approval_status_created already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_approval' AND INDEX_NAME = 'idx_device_approval_status_expires');
SET @sql = IF(@index_exists = 0, 'ALTER TABLE `device_approval` ADD INDEX `idx_device_approval_status_expires` (`approval_status`, `expires_at`)', 'SELECT "Index idx_device_approval_status_expires already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_approval' AND INDEX_NAME = 'idx_device_approval_partner_type_account');
SET @sql = IF(@index_exists = 0, 'ALTER TABLE `device_approval` ADD INDEX `idx_device_approval_partner_type_account` (`partner_type`, `account_id`)', 'SELECT "Index idx_device_approval_partner_type_account already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

# Additional indexes for device_auth_log table
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_auth_log' AND INDEX_NAME = 'idx_device_auth_log_action_created');
SET @sql = IF(@index_exists = 0, 'ALTER TABLE `device_auth_log` ADD INDEX `idx_device_auth_log_action_created` (`action`, `created_at`)', 'SELECT "Index idx_device_auth_log_action_created already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'device_auth_log' AND INDEX_NAME = 'idx_device_auth_log_status_change');
SET @sql = IF(@index_exists = 0, 'ALTER TABLE `device_auth_log` ADD INDEX `idx_device_auth_log_status_change` (`previous_status`, `new_status`)', 'SELECT "Index idx_device_auth_log_status_change already exists" as message');
PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
