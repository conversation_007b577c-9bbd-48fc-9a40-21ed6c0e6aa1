-- COD

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'cod_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cod_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cod_view_list', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cod_view_list');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cod_view_amount_bar', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cod_view_amount_bar');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cod_withdraw_view_detail', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cod_withdraw_view_detail');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cod_withdraw_request_detail', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cod_withdraw_request_detail');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cod_beneficiary_account_view_detail', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cod_beneficiary_account_view_detail');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cod_beneficiary_account_add_detail', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cod_beneficiary_account_add_detail');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'cod_beneficiary_account_edit_detail', NOW(), (SELECT id FROM permission WHERE name = 'cod_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'cod_beneficiary_account_edit_detail');

-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('cod_enable', 'cod_view_list', 'cod_view_amount_bar', 'cod_withdraw_view_detail', 'cod_withdraw_request_detail', 'cod_beneficiary_account_view_detail', 'cod_beneficiary_account_add_detail', 'cod_beneficiary_account_edit_detail')
	AND ar.account_role_type IN ('client');