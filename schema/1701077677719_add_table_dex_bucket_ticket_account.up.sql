CREATE TABLE `dex_bucket_ticket_account` (
    `dbta_id` varchar(36) NOT NULL,
    `dbta_account_id` int(11) unsigned not null,
    `dbta_bucket_ticket` int(11) not null default 0,
    `dbta_created_at` timestamp not null default current_timestamp,
    `dbta_created_by` int(11) not null,
    `dbta_updated_at` timestamp null default null on update current_timestamp,
    `dbta_updated_by` int(11),
    `dbta_last_assigned_at` timestamp null default null,
  PRIMARY KEY (`dbta_id`),
  KEY `dbta_account_id_IDX` (`dbta_account_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
