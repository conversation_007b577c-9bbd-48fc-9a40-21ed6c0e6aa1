CREATE TABLE IF NOT EXISTS `promo_discount_configuration` (
    `pdc_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `pdc_name` varchar(100) NOT NULL DEFAULT '',
    `pdc_origin` text NOT NULL,
    `pdc_destination` text NOT NULL,
    `pdc_product_type` varchar(100) NOT NULL,
    `pdc_minimal_gross_weight` double unsigned NOT NULL DEFAULT 0,
    `pdc_maximal_gross_weight` double unsigned NOT NULL DEFAULT 0,
    `pdc_start_date` timestamp NULL DEFAULT NULL,
    `pdc_end_date` timestamp NULL DEFAULT NULL,
    `pdc_discount_type` varchar(10) NOT NULL,
    `pdc_promo_discount` int(11) NOT NULL DEFAULT 0,
    `pdc_maximal_promo` int(11) unsigned DEFAULT 0,
    `pdc_parameter_calculation` varchar(100) NOT NULL,
    `pdc_applied_to` varchar(100) NOT NULL DEFAULT '', 
    `pdc_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `pdc_created_by` int(11) DEFAULT 0,
    `pdc_created_name` varchar(100) DEFAULT '',
    `pdc_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `pdc_updated_by` int(11) DEFAULT 0,
    `pdc_updated_name` varchar(100) DEFAULT '',
    PRIMARY KEY (`pdc_id`)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE INDEX pdc_name_IDX USING BTREE ON promo_discount_configuration (pdc_name);
CREATE INDEX pdc_start_date_IDX USING BTREE ON promo_discount_configuration (pdc_start_date);
CREATE INDEX pdc_end_date_IDX USING BTREE ON promo_discount_configuration (pdc_end_date);
CREATE INDEX pdc_minimal_gross_weight_IDX USING BTREE ON promo_discount_configuration (pdc_minimal_gross_weight);
CREATE INDEX pdc_maximal_gross_weight_IDX USING BTREE ON promo_discount_configuration (pdc_maximal_gross_weight);
CREATE INDEX pdc_product_type_IDX USING BTREE ON promo_discount_configuration (pdc_product_type);