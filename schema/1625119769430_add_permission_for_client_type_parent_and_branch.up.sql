INSERT INTO permission (name,created_at,parent_id)
	SELECT 'bulk_download_template_role', NOW(), (SELECT id FROM permission WHERE name = 'bulk_download_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_download_template_role');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'bulk_download_template_account', NOW(), (SELECT id FROM permission WHERE name = 'bulk_download_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_download_template_account');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'bulk_download_existing_permission_list', NOW(), (SELECT id FROM permission WHERE name = 'bulk_download_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_download_existing_permission_list');
    
INSERT INTO permission (name,created_at,parent_id)
	SELECT 'bulk_download_existing_role_list', NOW(), (SELECT id FROM permission WHERE name = 'bulk_download_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'bulk_download_existing_role_list');
    
-- insert mapping permission for parent
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'bulk_download_enable',
        'bulk_download_create',
        'bulk_download_template_role',
        'bulk_download_template_account',
        'bulk_download_existing_permission_list',
        'bulk_download_existing_role_list'
        )
	AND ar.account_role_client_type = "parent";
    
-- insert mapping permission for branch
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN (
        'shipment_bulk_print_resi_enable',
        'shipment_bulk_print_resi_create',
        'shipment_cn_manifest_enable',
        'shipment_cn_manifest_create',
        'bulk_download_enable',
        'bulk_download_create',
        'bulk_download_template_role',
        'bulk_download_template_account',
        'bulk_download_existing_permission_list',
        'bulk_download_existing_role_list',
        'booking_create_manual',
        'booking_edit'
        )
	AND ar.account_role_client_type = "branch";