CREATE TABLE `balance_minus_penalty` (
  `bmp_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `bmp_name` varchar(100) NULL,
  `bmp_partner_type`  varchar(100) NULL,
  `bmp_applied_to` varchar(100) NOT NULL,
  `bmp_description` varchar(220) NULL,
  `bmp_penalty_percentage_h1` DOUBLE NOT NULL DEFAULT '0',
  `bmp_penalty_percentage_hn` DOUBLE NOT NULL DEFAULT '0',
  `bmp_topup_deadline` TIME NOT NULL,
  `bmp_in_dayoff` tinyint(1) NOT NULL DEFAULT '0',
  `bmp_max_balance_minus` DOUBLE NOT NULL DEFAULT '0',
  `bmp_cod_embargo_type` varchar(220) NOT NULL default 'Origin',
  `bmp_status` varchar(100) NOT NULL,
  `bmp_created_by` int(11),
  `bmp_created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `bmp_updated_by` int(11),
  `bmp_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`bmp_id`),
  KEY `idx_bmp_cod_embargo_type` (`bmp_cod_embargo_type`),
  KEY `idx_bmp_in_dayoff` (`bmp_in_dayoff`),
  KEY `idx_bmp_status` (`bmp_status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;