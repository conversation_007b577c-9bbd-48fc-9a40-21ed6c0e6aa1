CREATE TABLE IF NOT EXISTS `region_city` (
    `region_city_id` int NOT NULL AUTO_INCREMENT,
    `region_city_region_code` varchar(100) NOT NULL DEFAULT '',
    `region_city_city_id` int NOT NULL,
    `region_city_status` ENUM('ACTIVE','INACTIVE') NOT NULL DEFAULT 'ACTIVE',
    `region_city_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `region_city_updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`region_city_id`)
)  ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE INDEX region_city_status_IDX USING BTREE ON `region_city` (region_city_status);
CREATE INDEX region_city_region_code_IDX USING BTREE ON `region_city` (region_city_region_code);

