INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) VALUES ('kejar_cuan_config_enable', NULL, CURRENT_TIMESTAMP, 0);
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) select 'kejar_cuan_config_list_view', NULL, CURRENT_TIMESTAMP, id from permission where name = "kejar_cuan_config_enable";
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) select 'kejar_cuan_config_detail_view', NULL, CURRENT_TIMESTAMP, id from permission where name = "kejar_cuan_config_enable";
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) select 'kejar_cuan_config_detail_edit', NULL, CURRENT_TIMESTAMP, id from permission where name = "kejar_cuan_config_enable";
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) select 'kejar_cuan_config_create', NULL, CURRENT_TIMESTAMP, id from permission where name = "kejar_cuan_config_enable";

INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) VALUES ('kejar_cuan_dashboard', NULL, CURRENT_TIMESTAMP, 0);
INSERT INTO `permission` (`name`, `description`, `created_at`, `parent_id`) select 'kejar_cuan_dashboard_detail', NULL, CURRENT_TIMESTAMP, id from permission where name = "kejar_cuan_dashboard";

INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "NPOS") from permission where name = "kejar_cuan_config_enable";
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "NPOS") from permission where name = "kejar_cuan_config_list_view";
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "NPOS") from permission where name = "kejar_cuan_config_detail_view";
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "NPOS") from permission where name = "kejar_cuan_config_detail_edit";
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "NPOS") from permission where name = "kejar_cuan_config_create";

INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "POS Super Admin") from permission where name = "kejar_cuan_dashboard";
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "POS Admin Booking") from permission where name = "kejar_cuan_dashboard";

INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "POS Super Admin") from permission where name = "kejar_cuan_dashboard_detail";
INSERT INTO `role_permission` (`permission_id`, `account_role_id`) select id, (select account_role_id from account_role where account_role_name = "POS Admin Booking") from permission where name = "kejar_cuan_dashboard_detail";

