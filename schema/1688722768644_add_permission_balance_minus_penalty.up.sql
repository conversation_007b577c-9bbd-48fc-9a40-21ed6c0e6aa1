INSERT INTO `permission`(`name`, `description`, `created_at`) 
SELECT 'balance_minus_penalty_enable', NULL, NOW()
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'balance_minus_penalty_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'balance_minus_penalty_list_view', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'balance_minus_penalty_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'balance_minus_penalty_list_view');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'balance_minus_penalty_detail_view', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'balance_minus_penalty_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'balance_minus_penalty_detail_view');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'balance_minus_penalty_detail_edit', NULL, NOW(), (Select `id` from `permission`
WHERE `name` = 'balance_minus_penalty_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'balance_minus_penalty_detail_edit');

