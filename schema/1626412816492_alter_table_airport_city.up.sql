ALTER TABLE `airport_city` ADD `airport_city_city_default` BOOLEAN NULL DEFAULT FALSE AFTER `airport_city_city_code`;


-- Update data existing
UPDATE `airport_city` SET `airport_city_city_default` = '1' WHERE `airport_city_airport_code` = 'HLP' AND `airport_city_city_code` = 'CGK';
UPDATE `airport_city` SET `airport_city_city_default` = '1' WHERE `airport_city_airport_code` = 'ATS' AND `airport_city_city_code` = 'BGR';
UPDATE `airport_city` SET `airport_city_city_default` = '1' WHERE `airport_city_airport_code` = 'CGK' AND `airport_city_city_code` = 'CGK';
UPDATE `airport_city` SET `airport_city_city_default` = '1' WHERE `airport_city_airport_code` = 'JOG' AND `airport_city_city_code` = 'JOG';
UPDATE `airport_city` SET `airport_city_city_default` = '1' WHERE `airport_city_airport_code` = 'HSI' AND `airport_city_city_code` = 'BDG';
UPDATE `airport_city` SET `airport_city_city_default` = '1' WHERE `airport_city_airport_code` = 'SKB' AND `airport_city_city_code` = 'SKB';