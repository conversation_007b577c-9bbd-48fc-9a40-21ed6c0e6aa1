CREATE TABLE `configuration_banner` (
  `cb_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cb_name` varchar(150) NOT NULL DEFAULT '',
  `cb_start_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cb_end_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cb_priority` int(11) NOT NULL DEFAULT 0,
  `cb_target` text NOT NULL,
  `cb_url_cta` varchar(150) NOT NULL DEFAULT '',
  `cb_image_url` varchar(150) NOT NULL DEFAULT '',
  `cb_status` enum('active','inactive') NOT NULL DEFAULT 'inactive',
  `cb_created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `cb_created_by` int(11) DEFAULT 0,
  `cb_created_name` varchar(200) DEFAULT '',
  `cb_updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `cb_updated_by` int(11) DEFAULT 0,
  `cb_updated_name` varchar(200) DEFAULT '',
   PRIMARY KEY (`cb_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
CREATE INDEX cb_id_IDX USING BTREE ON configuration_banner (cb_id);
CREATE INDEX cb_name_IDX USING BTREE ON configuration_banner (cb_name);
CREATE INDEX cb_start_date_IDX USING BTREE ON configuration_banner (cb_start_date);
CREATE INDEX cb_end_date_IDX USING BTREE ON configuration_banner (cb_end_date);
CREATE INDEX cb_status_IDX USING BTREE ON configuration_banner (cb_status);