ALTER TABLE `list_tag_group` ADD `list_tag_group_status_stt` VARCHAR(20) NOT NULL DEFAULT "" AFTER `list_tag_group_type_cod`;
ALTER TABLE `list_tag_group` ADD `list_tag_group_content` TEXT AFTER `list_tag_group_status_stt`;

-- Update data existing
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'BKD' WHERE `list_tag_group_action` = 'BKD (COD)';
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'POD' WHERE `list_tag_group_action` = 'POD (COD)';
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'DEX' WHERE `list_tag_group_action` = 'DEX (COD)';
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'DEL' WHERE `list_tag_group_action` = 'DEL (COD)';
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'CODREJ' WHERE `list_tag_group_action` = 'CODREJ (COD)';
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'BKD' WHERE `list_tag_group_action` = 'BKD (Non COD)';
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'POD' WHERE `list_tag_group_action` = 'POD (Non COD)';
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'DEX' WHERE `list_tag_group_action` = 'DEX (Non COD)';
UPDATE `list_tag_group` SET `list_tag_group_status_stt` = 'DEL' WHERE `list_tag_group_action` = 'DEL (Non COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n 
Paket COD Anda senilai {cod_amount} dari pengirim {shipper_name} telah dibooking oleh Lion Parcel \n 
pada {datetime} dengan No. STT {stt_number}. Paket Anda akan sampai dengan estimasi pengiriman {delivery_sla}. 
Terimakasih telah menggunakan layanan Lion Parcel ! :)\n
\n
Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'BKD (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {cod_amount} dari pengirim {shipper_name} dengan No. STT {stt_number} \n
dan estimasi pengiriman {delivery_sla} telah diterima oleh {recipient_name} pada {datetime}. \n
Kurir Lion Parcel yang mengantar paket Anda adalah {driver_name} dengan No. Telp \n
{driver_phone_number}. Terimakasih telah menggunakan Lion Parcel ! :)\n
\n
Jika paket belum Anda terima atau paket rusak mohon hubungi CS kami di 02180820072 atau Klaim \n
paket pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'POD (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {cod_amount} dari pengirim {shipper_name} dengan No. STT {stt_number} \n
dan estimasi pengiriman {delivery_sla} tidak terkirim karena {undelivered_reason} pada {datetime}. \n
Kurir Lion Parcel yang mengantar paket Anda adalah {driver_name} dengan No. Telp \n
{driver_phone_number}. Hubungi Customer Service kami untuk informasi lebih lanjut di 02180820072. Terimakasih telah menggunakan Lion Parcel ! :) \n
\n
Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'DEX (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {cod_amount} dari pengirim {shipper_name} dengan No. STT {stt_number} \n
dan estimasi pengiriman {delivery_sla} sedang dalam pengantaran ke alamat Anda pada {datetime}. \n
Kurir Lion Parcel yang mengantar paket Anda adalah {driver_name} dengan No. Telp \n
{driver_phone_number}. Mohon siapkan biaya COD sesuai dengan paket pesanan Anda. Terimakasih \n
telah menggunakan Lion Parcel ! :) \n
\n
Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'DEL (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket COD Anda senilai {cod_amount} dengan No. STT {stt_number} dan estimasi pengiriman \n
{delivery_sla} telah kami kembalikan kepada pengirim {shipper_name} karena {codrej_reason} \n
pada {datetime}. Kurir Lion Parcel yang mengantar paket Anda adalah {driver_name} dengan \n
No. Telp {driver_phone_number}. Terimakasih telah menggunakan layanan Lion Parcel ! :) \n
\n
Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'CODREJ (COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name}, \n
Paket Anda dari pengirim {shipper_name} dengan No. STT {stt_number} dan estimasi pengiriman {delivery_sla} telah diterima oleh {recipient_name} pada {datetime}. Kurir Lion Parcel yang  \n
mengantar paket Anda adalah {driver_name} dengan No. Telp {driver_phone_number}.  \n
Terimakasih telah menggunakan Lion Parcel ! :) \n
 \n
Jika paket belum Anda terima atau paket rusak mohon hubungi CS kami di 02180820072 atau Klaim  \n
paket pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'BKD (Non COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket Anda dari pengirim {shipper_name} dengan No. STT {stt_number} dan estimasi pengiriman {delivery_sla} telah diterima oleh {recipient_name} pada {datetime}. Kurir Lion Parcel yang \n
mengantar paket Anda adalah {driver_name} dengan No. Telp {driver_phone_number}. \n
Terimakasih telah menggunakan Lion Parcel ! :)\n
\n
Jika paket belum Anda terima atau paket rusak mohon hubungi CS kami di 02180820072 atau Klaim \n
paket pada Apps Lion Parcel http://bit.ly/LioApps'
WHERE `list_tag_group_action` = 'POD (Non COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket Anda dari pengirim {shipper_name} dengan No. STT {stt_number} dan estimasi pengiriman {delivery_sla} tidak terkirim karena {undelivered_reason} \n
pada {datetime}. Kurir Lion Parcel yang mengantar paket Anda adalah {driver_name} dengan \n
No. Telp {driver_phone_number}. Hubungi Customer Service kami untuk informasi lebih lanjut di\n 
021-80820072. Terimakasih telah menggunakan Lion Parcel ! :) \n
\n
Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'DEX (Non COD)';

UPDATE `list_tag_group` SET `list_tag_group_content` = 'Hi {consignee_name},\n
Paket Anda dari pengirim {shipper_name} dengan No. STT {stt_number} dan estimasi pengiriman {delivery_sla} sedang dalam pengantaran ke alamat Anda pada {datetime}. Kurir Lion Parcel yang \n
mengantar paket Anda adalah {driver_name} dengan No. Telp {driver_phone_number}. Mohon \n
siapkan biaya COD sesuai dengan paket pesanan Anda. Terimakasih telah menggunakan layanan\n 
Lion Parcel ! :) \n
\n
Cek status paket Anda di https://lionparcel.com/ atau cek pada Apps Lion Parcel http://bit.ly/LioApps' 
WHERE `list_tag_group_action` = 'DEL (Non COD)';