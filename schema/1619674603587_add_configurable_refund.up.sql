create table if not exists configurable_refund
(
	refund_id int auto_increment,
	refund_configurable_price_id int default 0 not null,
	refund_stt_last_status varchar(50) default '' not null,
	refund_amount double default 0 not null,
	refund_quantifier varchar(100) default '' not null,
	refund_amount_insurance double default 0 not null,
	refund_origin_city_exclude varchar(255) default '' not null,
	constraint configurable_refund_pk
		primary key (refund_id)
);

create index idx_refund_configurable_price_id
	on configurable_refund (refund_configurable_price_id);

create index idx_refund_stt_last_status
	on configurable_refund (refund_stt_last_status);

create index idx_refund_origin_city_exclude
	on configurable_refund (refund_origin_city_exclude);

alter table configurable_price modify configurable_price_type enum('heavy_weight_surcharge', 'insurance', 'woodpacking', 'stt_adjustment', 'refund') default 'heavy_weight_surcharge' not null;

INSERT INTO configurable_price (configurable_price_name, configurable_price_type,
                                           configurable_price_description, configurable_price_status,
                                           configurable_price_created_at, configurable_price_created_by,
                                           configurable_price_updated_at, configurable_price_updated_by)
VALUES ('REFUND CONFIGURATION', 'refund',
        'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.',
        'active', NOW(), 1, NOW(), 1);


INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,
                                            refund_quantifier, refund_amount_insurance, refund_origin_city_exclude)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'BKD', 100,
        'base_rate,shipping_surcharge', 100, 'no-city');

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,
                                            refund_quantifier, refund_amount_insurance, refund_origin_city_exclude)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'PUP', 100,
        'base_rate,shipping_surcharge', 100, 'ftz');

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,
                                            refund_quantifier, refund_amount_insurance, refund_origin_city_exclude)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'STI', 100,
        'base_rate,shipping_surcharge', 100, 'non-ftz');

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,
                                            refund_quantifier, refund_amount_insurance, refund_origin_city_exclude)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'BAGGING', 100,
        'base_rate,shipping_surcharge', 100, 'non-ftz,CGK');

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,
                                            refund_quantifier, refund_amount_insurance, refund_origin_city_exclude)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'CARGO PLANE', 100,
        'base_rate,shipping_surcharge', 100, 'ftz,DPS,CGK');

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,
                                            refund_quantifier, refund_amount_insurance, refund_origin_city_exclude)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'CARGO TRUCK', 100,
        'base_rate,shipping_surcharge', 100, 'ftz,DPS,CGK');

INSERT INTO configurable_refund (refund_configurable_price_id, refund_stt_last_status, refund_amount,
                                            refund_quantifier, refund_amount_insurance, refund_origin_city_exclude)
VALUES ((select configurable_price_id from configurable_price where configurable_price_type = 'refund'), 'CARGO TRAIN', 100,
        'base_rate,shipping_surcharge', 100, 'non-ftz');
