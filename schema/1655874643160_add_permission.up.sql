INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'internal_outgoing_incoming_cargo_detail_edit',
    NOW(),
    (SELECT id FROM permission WHERE name = 'internal_outgoing_incoming_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'internal_outgoing_incoming_cargo_detail_edit');

INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'internal_outgoing_incoming_cargo_detail_view',
    NOW(),
    (SELECT id FROM permission WHERE name = 'internal_outgoing_incoming_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'internal_outgoing_incoming_cargo_detail_view');

INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'internal_outgoing_incoming_bagging_detail_edit',
    NOW(),
    (SELECT id FROM permission WHERE name = 'internal_outgoing_incoming_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'internal_outgoing_incoming_bagging_detail_edit');

INSERT INTO permission (name, created_at, parent_id)
  SELECT
    'internal_outgoing_incoming_bagging_detail_view',
    NOW(),
    (SELECT id FROM permission WHERE name = 'internal_outgoing_incoming_enable')
  WHERE
    NOT EXISTS (SELECT name FROM permission WHERE name = 'internal_outgoing_incoming_bagging_detail_view');


DELETE FROM role_permission WHERE permission_id = (SELECT id FROM permission WHERE name = 'cargo_detail_edit') and account_role_id = 1;
DELETE FROM role_permission WHERE permission_id = (SELECT id FROM permission WHERE name = 'cargo_detail_view') and account_role_id = 1;
DELETE FROM role_permission WHERE permission_id = (SELECT id FROM permission WHERE name = 'bagging_detail_edit') and account_role_id = 1;
DELETE FROM role_permission WHERE permission_id = (SELECT id FROM permission WHERE name = 'bagging_detail_view') and account_role_id = 1;


INSERT INTO role_permission (`permission_id`, `account_role_id`) VALUES (
    (SELECT id FROM permission WHERE name = 'internal_outgoing_incoming_cargo_detail_edit'), 
    (SELECT account_role_id FROM account_role WHERE account_role_type = 'internal'  AND account_role_name = 'Helpdesk')
);

INSERT INTO role_permission (`permission_id`, `account_role_id`) VALUES (
    (SELECT id FROM permission WHERE name = 'internal_outgoing_incoming_cargo_detail_view'), 
    (SELECT account_role_id FROM account_role WHERE account_role_type = 'internal'  AND account_role_name = 'Helpdesk')
);

INSERT INTO role_permission (`permission_id`, `account_role_id`) VALUES (
    (SELECT id FROM permission WHERE name = 'internal_outgoing_incoming_bagging_detail_edit'), 
    (SELECT account_role_id FROM account_role WHERE account_role_type = 'internal'  AND account_role_name = 'Helpdesk')
);

INSERT INTO role_permission (`permission_id`, `account_role_id`) VALUES (
    (SELECT id FROM permission WHERE name = 'internal_outgoing_incoming_bagging_detail_view'), 
    (SELECT account_role_id FROM account_role WHERE account_role_type = 'internal'  AND account_role_name = 'Helpdesk')
);