INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bagging_grouping_enable', '', NOW(), 0
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bagging_grouping_enable');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bagging_grouping_list_view', '', NOW(), (select id from permission where name = 'bagging_grouping_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bagging_grouping_list_view');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bagging_grouping_detail_view', '', NOW(), (select id from permission where name = 'bagging_grouping_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bagging_grouping_detail_view');

INSERT INTO `permission`(`name`, `description`, `created_at`, `parent_id`) 
SELECT 'bagging_grouping_detail_edit', '', NOW(), (select id from permission where name = 'bagging_grouping_enable')
WHERE NOT EXISTS(SELECT * FROM `permission` WHERE `name` = 'bagging_grouping_detail_edit');