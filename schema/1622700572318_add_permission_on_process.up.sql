-- ON PROCESS STT

-- insert to table permission
INSERT INTO permission (name,created_at)
	SELECT 'on_process_stt_enable', NOW()
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'on_process_stt_enable');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'on_process_stt_list_view', NOW(), (SELECT id FROM permission WHERE name = 'on_process_stt_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'on_process_stt_list_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'on_process_stt_detail_view', NOW(), (SELECT id FROM permission WHERE name = 'on_process_stt_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'on_process_stt_detail_view');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'on_process_stt_detail_edit', NOW(), (SELECT id FROM permission WHERE name = 'on_process_stt_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'on_process_stt_detail_edit');

INSERT INTO permission (name,created_at,parent_id)
	SELECT 'on_process_stt_detail_cancel', NOW(), (SELECT id FROM permission WHERE name = 'on_process_stt_enable')
	WHERE NOT EXISTS (SELECT name FROM permission WHERE name = 'on_process_stt_detail_cancel');


-- insert mapping permission
INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('on_process_stt_enable','on_process_stt_list_view','on_process_stt_detail_view')
	AND ar.account_role_type IN ('console','sub-console');


INSERT INTO role_permission (permission_id, account_role_id)
SELECT
	p.id, ar.account_role_id 
FROM
	permission p
CROSS JOIN account_role ar
WHERE
	p.name IN ('on_process_stt_detail_edit','on_process_stt_detail_cancel')
	AND ar.account_role_type = 'console';