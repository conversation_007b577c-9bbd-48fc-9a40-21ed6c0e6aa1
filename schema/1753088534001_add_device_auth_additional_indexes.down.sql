# Drop Additional Indexes for Device Authentication Tables
# This migration removes the additional indexes

# Drop additional indexes for device_auth table
ALTER TABLE `device_auth` DROP INDEX `idx_device_auth_partner_hub_account`;
ALTER TABLE `device_auth` DROP INDEX `idx_device_auth_partner_type_account`;
ALTER TABLE `device_auth` DROP INDEX `idx_device_auth_device_type`;

# Drop additional indexes for device_approval table  
ALTER TABLE `device_approval` DROP INDEX `idx_device_approval_status_created`;
ALTER TABLE `device_approval` DROP INDEX `idx_device_approval_status_expires`;
ALTER TABLE `device_approval` DROP INDEX `idx_device_approval_partner_type_account`;

# Drop additional indexes for device_auth_log table
ALTER TABLE `device_auth_log` DROP INDEX `idx_device_auth_log_action_created`;
ALTER TABLE `device_auth_log` DROP INDEX `idx_device_auth_log_approval_device`;
ALTER TABLE `device_auth_log` DROP INDEX `idx_device_auth_log_status_change`;
