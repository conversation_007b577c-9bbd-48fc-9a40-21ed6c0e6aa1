CREATE TABLE `cs_account_log` (
    `cal_id` varchar(36) NOT NULL,
    `cal_account_id` int(11) not null,
    `cal_action` varchar(40) not null,
    `cal_status` varchar(20) not null,
    `cal_created_at` timestamp not null default current_timestamp,
  PRIMARY KEY (`cal_id`),
  <PERSON><PERSON>Y `cal_action_IDX` (`cal_action`),
  <PERSON>EY `cal_status_IDX` (`cal_status`),
  <PERSON>EY `cal_account_id_IDX` (`cal_account_id`),
  KEY `cal_created_at_IDX` (`cal_created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
