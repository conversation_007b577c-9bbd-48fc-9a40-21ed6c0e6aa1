package keys

import (
	"crypto/rsa"
	"io/ioutil"
	"os"

	"github.com/dgrijalva/jwt-go"
)

const (
	privateKeyPath            = "./config/app.rsa"
	publicKeyPath             = "./config/app.rsa.pub"
	privateKeyIntegrationPath = "./config/app-integrate.rsa"
	publicKeyIntegrationPath  = "./config/app-integrate.rsa.pub"
	googleKeyPath             = "./config/horde_credential.json"
)

var (
	verifyKey *rsa.PublicKey
	signKey   *rsa.PrivateKey
)

// InitPublicKey return *rsa.PublicKey
func InitPublicKey() (*rsa.PublicKey, error) {
	verifyBytes, err := ioutil.ReadFile(publicKeyPath)
	if err != nil {
		return nil, err
	}

	verifyKey, err = jwt.ParseRSAPublicKeyFromPEM(verifyBytes)
	if err != nil {
		return nil, err
	}
	return verifyKey, nil
}

// InitPrivateKey return *rsa.PrivateKey
func InitPrivateKey() (*rsa.PrivateKey, error) {
	signBytes, err := ioutil.ReadFile(privateKeyPath)
	if err != nil {
		return nil, err
	}

	signKey, err = jwt.ParseRSAPrivateKeyFromPEM(signBytes)
	if err != nil {
		return nil, err
	}
	return signKey, nil
}

// InitPublicIntegrateKey return *rsa.PublicKey
func InitPublicIntegrateKey() (*rsa.PublicKey, error) {
	verifyBytes, err := ioutil.ReadFile(publicKeyIntegrationPath)
	if err != nil {
		return nil, err
	}

	verifyKey, err = jwt.ParseRSAPublicKeyFromPEM(verifyBytes)
	if err != nil {
		return nil, err
	}
	return verifyKey, nil
}

// InitIntegratePrivateKey return *rsa.PrivateKey
func InitIntegratePrivateKey() (*rsa.PrivateKey, error) {
	signBytes, err := ioutil.ReadFile(privateKeyIntegrationPath)
	if err != nil {
		return nil, err
	}

	signKey, err = jwt.ParseRSAPrivateKeyFromPEM(signBytes)
	if err != nil {
		return nil, err
	}
	return signKey, nil
}

// GoogleKey ..
func GoogleKey() string {
	return googleKeyPath
}

func GoogleGenesisMobileKey() string {
	if key := os.Getenv("GOOGLE_GENESIS_MOBILE_KEY"); key != "" {
		return key
	}
	return "./config/horde_genesis_mobile_credential.json"
}

// PubSubProjectID ..
func PubSubProjectID() string {
	return os.Getenv(`PUBSUB_PROJECT_ID`)
}
