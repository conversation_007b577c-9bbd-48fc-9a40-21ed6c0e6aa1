SERVICE_NAME=horde
DEVELOPMENT= 0.0.0.0
ENVIRONMENT=dev-genesis
PORT= 8080

DB_MASTER_HOST= $DB_MASTER_HOST
DB_MASTER_USERNAME= horde
DB_MASTER_PASSWORD= $HORDE_PASSWORD_DB
DB_MASTER_NAME= horde
DB_MASTER_PORT= 3306

DB_SLAVE_HOST= $DB_SLAVE_HOST
DB_SLAVE_USERNAME= horde
DB_SLAVE_PASSWORD= $HORDE_PASSWORD_DB
DB_SLAVE_NAME= horde
DB_SLAVE_PORT= 3306

DB_CLONE_HOST= $DB_CLONE_HOST
DB_CLONE_USERNAME= horde
DB_CLONE_PASSWORD= $HORDE_PASSWORD_DB
DB_CLONE_NAME= horde
DB_CLONE_PORT= 3306

DB_CLONE_BL_HOST= localhost
DB_CLONE_BL_USERNAME= root
DB_CLONE_BL_PASSWORD= root
DB_CLONE_BL_NAME= hordeclone
DB_CLONE_BL_PORT= 3306

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB=1
REDIS_PASSWORD=
REDIS_TLS=false
REDIS_DB_PROMO=2

# token verification
USE_NEW_TOKEN_VERIFICATION=true
REDIS_HOST_TOKEN_VERIFICATION=127.0.0.1:6379
REDIS_PASSWORD_TOKEN_VERIFICATION=
REDIS_DB_TOKEN_VERIFICATION=8
REDIS_MAX_RETRIES_TOKEN_VERIFICATION=0
REDIS_DIAL_TIMEOUT_SECOND_TOKEN_VERIFICATION=0
REDIS_INSECURE_SKIP_VERIFY_TOKEN_VERIFICATION=true

TOKEN_NAME="X-LionParcel-AccessToken"
TOKEN_TYPE=Bearer
TOKEN_TEMPORARY_LIFETIME=300
ACCESS_TOKEN_LIFETIME=24

ELASTIC_URL=https://de1f8276dcc843d6931ba5ca65508190.asia-southeast1.gcp.elastic-cloud.com:9243
ELASTIC_USERNAME=$ELASTIC_USERNAME
ELASTIC_PASSWORD=$ELASTIC_PASSWORD

OPENSEARCH_ENABLE=true
OPENSEARCH_URL=https://vpc-genesis-dev-opensearch-txqbeilthlpf3tp4v2ull2n45a.ap-southeast-1.es.amazonaws.com
OPENSEARCH_USERNAME=
OPENSEARCH_PASSWORD=
OPENSEARCH_VIEW_DISTRICT_V3_ENABLE=true

EMAIL_SOURCE= <EMAIL>
EMAIL_NAME= Lion Parcel
EMAIL_URL= https://api.sendgrid.com
EMAIL_END_POINT= /v3/mail/send
EMAIL_API_KEY= $EMAIL_API_KEY

BULK_DEFAULT_PASSWORD=123456
FRONT_END_DEV_URL=https://dev-genesis.thelionparcel.com
FRONT_END_STG_URL=https://stg-genesis.thelionparcel.com
FRONT_END_PRD_URL=https://genesis.thelionparcel.com
EMAIL_TEMPLATE_ID_RESET_PASSWORD=d-0821524760f04ee9bb11515157a8a118
EMAIL_TEMPLATE_ID_SEND_OTP=

BUCKET_ANNOUNCEMENT=dev-genesis-announcement

EMAIL_TEMPLATE_ID_NEW_ACCOUNT=

ELASTIC_APM_SERVICE_NAME=horde
ELASTIC_APM_SERVER_URL=https://d98920e1473442b7a2fc31299b635b0d.apm.us-east-1.aws.cloud.es.io:443
ELASTIC_APM_SECRET_TOKEN=lxo2Tr1ZlTyM62no0w

BACK_END_DEV_URL=https://api.dev-genesis.lionparcel.com
BACK_END_STG_URL=https://api.stg-genesis.lionparcel.com
BACK_END_PRD_URL=https://api.genesis.lionparcel.com

HYDRA_URL=https://api.stg-genesis.lionparcel.com/hydra
GOBER_URL=https://api.stg-genesis.lionparcel.com/gober

API_KEY_ELEXYS

API_URL_ELEXYS

ELEXYS_ACTIVE=true

WEBHOOK_SLACK=*******************************************************************************
SLACK_NOTIFIER=true
SIKAT_MAX_THRESHOLD_GROSS=3.0

MONGODB_URI=mongodb://localhost:27017/?authSource=admin&maxPoolSize=20&maxIdleTimeMS=60000
MONGODB_USER=root
MONGODB_PW=test
MONGODB_AUTH_SOURCE=admin
MONGODB_DATABASE=horde

DEFAULT_LIMIT_PAGINATION=5

PRIVATE_PORT=8081

DEFAULT_LIFE_TIME_PERMISSION_EXP=6

ENABLE_CLIENT_RATE_TO_ARCHIVE=true

ENABLE_STORE_NOSQL=false
ACCESS_TOKEN_LIFETIME_INTEGRATION=1
ENABLE_NEW_TARIFF_BIGPACK=true

SCHEDULER_CLIENT_ID=2a6cc254-7ce6-477d-9e53-216dcc261514
SCHEDULER_CLIENT_SECRET=19bf96ec-dc21-4df4-9024-c985b83159f4

CPC_DAY_OF_MONTH_SCHEDULE=8

DISTRICT_CLIENT_DEFAULT=2047

YM_SECRET_CODE=

USERNAME_BASIC_AUTHENTICATION=test
PASSWORD_BASIC_AUTHENTICATION=123

ALGO_AUTH_USERNAME=
ALGO_AUTH_PASSWORD=
ALGO_AUTH_ROLE=GENESIS
ALGO_URL=https://algo-api-dev.lionparcel.com

ODOO_URL=https://erp-development.thelionparcel.com
ODOO_API_KEY=ebfb7ff0-b2f6-41c8-bef3-4fba17be410c

EMAIL_NOTIF_RATE_VERSION_EXP=<EMAIL>
SIGNATURE_NOTIF_RATE_VERSION_EXP=gUkXp2s5u8x/A?D(G+KbPeShVmYq3t6w


DEFAULT_BATCH_EVENT_LIMIT=1
DEFAULT_BATCH_EVENT_RETRY_LIMIT=3
HORDE_EVENT_PULL_WORKER_TOPIC_ID=horde_event_pull_worker
ENABLE_BATCH_UPLOAD_RATE=true
CUSTOM_UPLOAD_RATE_BATCH_ROW_COUNT=10000
CUSTOM_UPLOAD_RATE_BATCH_MIN_ROW=1000
BATCH_UPLOAD_RATE_CACHE_DURATION=5
CLOUDBEES_KEY=64b519dbb8bd8b8ffad29cea


PEGASUS_URL=https://api-dev-middleware.thelionparcel.com
PEGASUS_AUTH_USERNAME=dev-pegasus
PEGASUS_AUTH_PASSWORD=MiddL3w4re

IS_USE_FLOW_PRE_PROCESSING=true



TRANSPORT_TYPE_USE_LIMIT=
TRANSPORT_TYPE_USE_GROUPING=
	
METABASE_URL=http://*************:9200
METABASE_AUTH_USERNAME='lpdata'
METABASE_AUTH_PASSWORD='lpdatateams'

CACHE_TIMELIMIT_PROFILE=1

#module or usecase
ENABLE_TARIFF_DISPATCHER=true

MAX_PICKUP_CORPORATE_ADDRESS=10

USE_MYSQL_IN_CUSTOMER_v2=true

KEY_ENCRYPTION_DATABASE=thisis32bitlongpassphraseimusing

CARGO_SEARCH_FLIGHT_MAX_FLIGHT=4
TRANSACTION_CODES="01,02,03,04,05,06,07,08,09" # only support two digit

BULK_BOOKING_MAX_ROW=500
BULK_BOOKING_MAX_QUOTA=1000
BULK_BOOKING_PROCESS_TIMEOUT_SECOND=3600
BULK_BOOKING_MAX_SIZE_MB=5

PEGASUS_BASIC_AUTH_USERNAME=
PEGASUS_BASIC_AUTH_PASSWORD=

# ON SECOND
OTP_VALID_TIME_LIMIT=120
OTP_VALID_TIME_EXPIRED=10800

# Google reCAPTCHA
GOOGLE_RECAPTCHA_SECRET_KEY="6Ldvs3srAAAAAGeo3I1v5ub4yVwHeu5fjRKRWfGM"
GOOGLE_RECAPTCHA_SITE_KEY="6Ldvs3srAAAAAHQi0nZ_bdtLMR3mouL2n14IhakU"

MESSAGING_SENDER_URL="http://*************:28030/apiHttpLionParcel/receive.php"
MESSAGING_SENDER_UID="123"
MESSAGING_SENDER_USERNAME="asdfasdfas"
MESSAGING_SENDER_PASSWORD="adsfasfdas"
MESSAGING_OTP_TEMPLATE="Hi {{.Username}},\n\nUntuk alasan kemanan, Anda diminta untuk melakukan verifikasi email dan mengatur ulang password baru. Berikut adalah kode verifikasi akun Genesis Anda:\n\n{{.OTPCode}}\n\nOTP ini berlaku {{ .Hour }} jam dari pesan ini diterima."
MESSAGING_SENDER_ID=LIONPARCEL

EMAIL_TEMPLATE_ID_VERIFICATION=""
MESSAGING_SUBJECT_WITHDRAW_SALDO=""
MESSAGING_SUBJECT_ADJUST_SALDO=""
MESSAGING_SUBJECT_CHANGE_PASSWORD=""
MESSAGING_TITLE_WITHDRAW_SALDO=""
MESSAGING_TITLE_ADJUST_SALDO=""
MESSAGING_TITLE_CHANGE_PASSWORD=""

DEVICE_ENCRYPTION_SECRET_KEY="my32digitkey12345678901234567890"
DEVICE_TOKEN_LIFETIME="36000h"
DEVICE_MASTER_TEMPORARY_TOKEN_LIFETIME="2m"
DEVICE_JWT_SECRET="ABUUUU"
