package flag_management

import (
	"os"

	"github.com/rollout/rox-go/v5/server"
)

type FlagManagement interface {
	CloudBees() *server.Rox
}

type flagManagement struct {
	cloudBees *server.Rox
}

func (c *flagManagement) CloudBees() *server.Rox {
	return c.cloudBees
}

// Ref: https://docs.cloudbees.com/docs/cloudbees-feature-management/latest/getting-started/go-sdk
func InitFlagManagementCloudBees() *FlagsModel {
	inst := new(flagManagement)
	options := server.NewRoxOptions(server.RoxOptionsBuilder{
		DevModeKey: os.Getenv(`CLOUDBEES_DEV_MODE_KEY`),
	})
	rox := server.NewRox()

	// Register the flags container
	rox.Register(os.Getenv(`SERVICE_NAME`), flags.CloudBees)

	// Setup the environment key
	<-rox.Setup(os.Getenv(`CLOUDBEES_KEY`), options)

	inst.cloudBees = rox

	return flags
}
