package flag_management

import "github.com/rollout/rox-go/v5/server"

type FlagsModel struct {
	CloudBees *FlagsCloudBeesModel
	// add third-party model
}

type FlagsCloudBeesModel struct {
	EnableMappingS3Link                  server.RoxFlag
	DayNotifRateVersionExp               server.RoxInt
	DexAssessmentCSCutOffTimeStart       server.RoxString
	DexAssessmentCSCutOffTimeEnd         server.RoxString
	MinConfigurableTopUpFee              server.RoxInt
	TopUpCashbackPermissionRoles         server.RoxString
	SamedayCutOffTime                    server.RoxString
	EnableHeavyWeightSurchargeValidation server.RoxFlag
	CaptchaScoreThreshold                server.RoxDouble
	OtpEnableChangePassword              server.RoxFlag
	InternalDomainEmail                  server.RoxString
	EnableDeviceMaster                   server.RoxFlag
	EnableDeviceMasterAutoApproval       server.RoxFlag
}

var flags = &FlagsModel{
	CloudBees: &FlagsCloudBeesModel{
		EnableMappingS3Link:                  server.NewRoxFlag(false),
		DayNotifRateVersionExp:               server.NewRoxInt(7, []int{7, 14, 30}),
		DexAssessmentCSCutOffTimeStart:       server.NewRoxString("", []string{}),
		DexAssessmentCSCutOffTimeEnd:         server.NewRoxString("", []string{}),
		MinConfigurableTopUpFee:              server.NewRoxInt(1000, []int{}),
		TopUpCashbackPermissionRoles:         server.NewRoxString("", []string{}),
		SamedayCutOffTime:                    server.NewRoxString("", []string{}),
		EnableHeavyWeightSurchargeValidation: server.NewRoxFlag(false),
		CaptchaScoreThreshold:                server.NewRoxDouble(0.7, []float64{}),
		OtpEnableChangePassword:              server.NewRoxFlag(false),
		InternalDomainEmail:                  server.NewRoxString("", []string{}),
		EnableDeviceMaster:                   server.NewRoxFlag(false),
		EnableDeviceMasterAutoApproval:       server.NewRoxFlag(false),
	},
}
