package recaptcha_enterprise

import (
	"context"
	"log"

	recaptcha "cloud.google.com/go/recaptchaenterprise/v2/apiv1"
	"github.com/Lionparcel/horde/config/keys"
	"google.golang.org/api/option"
)

type RecaptchaEnterprise struct {
	Client *recaptcha.Client
}

func New() *RecaptchaEnterprise {
	ctx := context.Background()
	client, err := recaptcha.NewClient(ctx, option.WithCredentialsFile(keys.GoogleGenesisMobileKey()))
	if err != nil {
		log.Fatalf("Error creating reCAPTCHA client: %v", err)
	}

	return &RecaptchaEnterprise{
		Client: client,
	}
}
