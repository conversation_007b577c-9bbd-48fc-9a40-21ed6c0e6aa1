package cache

import (
	"context"
	"crypto/tls"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	goredislib "github.com/redis/go-redis/v9"
)

type DistributedLock struct {
	redsync *redsync.Redsync
	redis   *goredislib.Client
}

func (d *DistributedLock) Lock(key string, lockDuration time.Duration) error {
	mutex := d.redsync.NewMutex(key, redsync.WithExpiry(
		lockDuration,
	))
	return mutex.TryLock()
}
func (d *DistributedLock) Unlock(ctx context.Context, key string) {
	d.redis.Del(ctx, key)
}

func NewDistributedLock() (*DistributedLock, error) {
	tlsSecured, err := strconv.ParseBool(os.Getenv("REDIS_TLS"))
	if err != nil {
		return nil, err
	}

	var conf *tls.Config

	// force checking for unsecured aws redis
	if tlsSecured {
		conf = &tls.Config{
			//nolint:gosec
			InsecureSkipVerify: tlsSecured,
		}
	} else {
		conf = nil
	}

	redisdb, _ := strconv.Atoi(os.Getenv("REDIS_DB"))
	client := goredislib.NewClient(&goredislib.Options{
		Addr:      fmt.Sprintf("%v:%v", os.Getenv("REDIS_HOST"), os.Getenv("REDIS_PORT")),
		Password:  os.Getenv("REDIS_PASSWORD"),
		DB:        redisdb,
		TLSConfig: conf,
	})
	pool := goredis.NewPool(client)
	rs := redsync.New(pool)

	return &DistributedLock{
		redsync: rs,
		redis:   client,
	}, nil
}
