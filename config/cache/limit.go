package cache

import "github.com/go-redis/redis"

func (r Conn) LimitWithScript(key string, maxLimit int64) (any, error) {
	script := redis.NewScript(`
		local current = redis.call("INCR", KEYS[1])
		if current > tonumber(ARGV[1]) then
			return -1
		else
			return current
		end
	`)

	return script.Run(r.Client, []string{key}, maxLimit).Result()
}

func (r Conn) Decr(key string) (int64, error) {
	return r.Client.Decr(key).Result()
}
