package firebase

import (
	"context"
	"log"

	"github.com/Lionparcel/horde/config/keys"
	"github.com/Lionparcel/horde/shared/logger"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"google.golang.org/api/option"
)

type FirebaseCloudMessagingRequest struct {
	Token       []string
	Title       string
	Body        string
	Icon        string
	Badge       string
	Image       string
	ClickAction string
	Data        map[string]string
}

type FirebaseFcm interface {
	PublishMessage(ctx context.Context, req *FirebaseCloudMessagingRequest) (*messaging.BatchResponse, error)
}

type FirebaseFcmCtx struct {
	Client *messaging.Client
}

func NewFirebaseCloudMessaging() FirebaseFcm {
	ctx := context.Background()
	app, err := firebase.NewApp(ctx, nil, option.WithCredentialsFile(keys.GoogleKey()))
	if err != nil {
		logger.Panic(err)
	}

	client, err := app.Messaging(ctx)
	if err != nil {
		log.Fatalf("error getting Messaging client: %v\n", err)
	}

	fcmCtx := FirebaseFcmCtx{
		Client: client,
	}
	return &fcmCtx
}

func (c *FirebaseFcmCtx) PublishMessage(ctx context.Context, req *FirebaseCloudMessagingRequest) (*messaging.BatchResponse, error) {

	AssetNotificationCargo := req.Icon

	if req.Icon == "" {
		AssetNotificationCargo = "https://storage.googleapis.com/prd-genesis/cargo-notif/icon-notif-cargo.png"
	}

	response, err := c.Client.SendEachForMulticast(ctx, &messaging.MulticastMessage{
		Webpush: &messaging.WebpushConfig{
			Data: req.Data,
			Notification: &messaging.WebpushNotification{
				Title: req.Title,
				Body:  req.Body,
				Icon:  AssetNotificationCargo,
				Badge: req.Badge,
				Image: req.Image,
			},
		},
		Tokens: req.Token,
	})

	if err != nil {
		return nil, err
	}
	return response, nil
}
