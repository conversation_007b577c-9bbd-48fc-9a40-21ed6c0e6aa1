// genesis_mobile_fcm.go
package firebase

import (
	"context"
	"log"

	firebase "firebase.google.com/go/v4"
	"firebase.google.com/go/v4/messaging"
	"github.com/Lionparcel/horde/config/keys"
	"google.golang.org/api/option"
)

type GenesisMobileFcmCtx struct {
	Client *messaging.Client
}

// NewGenesisMobileFcm initializes a new Firebase Cloud Messaging client for the Genesis mobile application.
func NewGenesisMobileFcm() FirebaseFcm {
	ctx := context.Background()
	app, err := firebase.NewApp(ctx, nil, option.WithCredentialsFile(keys.GoogleGenesisMobileKey()))
	if err != nil {
		log.Fatalf("error initializing Firebase app: %v\n", err)
	}

	client, err := app.Messaging(ctx)
	if err != nil {
		log.Fatalf("error getting Messaging client: %v\n", err)
	}

	return &GenesisMobileFcmCtx{
		Client: client,
	}
}

// PublishMessage sends a notification message to the specified tokens.
func (c *GenesisMobileFcmCtx) PublishMessage(ctx context.Context, req *FirebaseCloudMessagingRequest) (*messaging.BatchResponse, error) {
	message := &messaging.MulticastMessage{
		Notification: &messaging.Notification{
			Title: req.Title,
			Body:  req.Body,
		},
		Data:   req.Data,
		Tokens: req.Token,
		Android: &messaging.AndroidConfig{
			Notification: &messaging.AndroidNotification{
				ClickAction: req.ClickAction,
			},
		},
	}

	response, err := c.Client.SendEachForMulticast(ctx, message)
	if err != nil {
		return nil, err
	}
	return response, nil
}
