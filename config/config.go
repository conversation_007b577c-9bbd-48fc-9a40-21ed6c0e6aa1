package config

import (
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/Lionparcel/horde/shared/crypto"
	"github.com/Lionparcel/horde/shared/utils"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"go.elastic.co/apm/module/apmmongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"github.com/Lionparcel/horde/config/database"
	"github.com/Lionparcel/horde/config/elastic"
	"github.com/Lionparcel/horde/config/email"
	"github.com/Lionparcel/horde/config/flag_management"
	"github.com/Lionparcel/horde/config/keys"
	"github.com/Lionparcel/horde/config/pubsub"
	"github.com/Lionparcel/horde/shared/logger"

	"github.com/Lionparcel/horde/config/cache"
)

// Config struct
type Config struct {
	serviceName string
	environment string
	debug       bool
	publicPort  int
	privatePort int
	frontEndUrl string

	stream bool
	rpc    bool
	rest   bool

	redis RedisConfig

	token TokenConfig

	tokenIntegration TokenIntegrationConfig

	db database.GormDatabase

	noSqlDB database.MongoDB

	openSearchDB database.OpenSearch

	dynamoDielector database.DynamoDialector
	mongoDielector  database.MongoDialector

	cache      cache.Client
	cachePromo cache.Client

	es elastic.ElasticClient

	mailTemplateIDResetPassword string

	mail email.EmailSendGrid

	usernameBasicAuth string
	passwordBasicAuth string

	googleCredential *GoogleCredential
	pubsub           PubSub

	cloudbeesKey        string
	cloudbeesDevModeKey string
	flagManagement      *flag_management.FlagsModel

	distributedLock *cache.DistributedLock
}

type RedisConfig struct {
	Host, Port, Password, DB, TLS string
}

type PemKey struct {
	Cert    string
	Private string
}

type TokenConfig struct {
	Name                               string
	Type                               string
	TemporaryLifetime                  time.Duration
	AccessTokenLifeTime                time.Duration
	DeviceMasterTemporaryTokenLifetime time.Duration
	DeviceTokenLifetime                time.Duration
	PrivateKey                         *rsa.PrivateKey
	PublicKey                          *rsa.PublicKey
	DeviceJWTSecret                    string
}

type TokenIntegrationConfig struct {
	PrivateKey *rsa.PrivateKey
	PublicKey  *rsa.PublicKey
}

type GoogleCredential struct {
	ServiceAccount string `json:"service_account"`
	ProjectID      string `json:"project_id"`
	ClientEmail    string `json:"client_email"`
	ClientID       string `json:"client_id"`
}

type PubSub struct {
	GCP pubsub.GooglePubsub
	// eg. AWS pubsub.AwsPubsub
}

// NewConfig func
func NewConfig() *Config {
	cfg := new(Config)

	cfg.InitEnvironmentService()
	cfg.ConnectDB()
	cfg.InitRedis()
	cfg.InitToken()
	cfg.InitTokenItegration()
	cfg.InitElastic()
	cfg.InitEmail()
	cfg.ConnectNoSqlDB()
	cfg.InitOpenSearch()
	//cfg.InitTracer()

	cfg.InitGoogleCredential()
	cfg.InitPubsub()
	cfg.InitBasicAuth()
	cfg.SchedulerMakeInactiveBanner()
	cfg.InitFlagManagementCloudBees()
	cfg.InitDistributedLock()

	return cfg
}

func (c *Config) InitEnvironmentService() {
	c.cloudbeesKey = os.Getenv(`CLOUDBEES_KEY`)
	if c.cloudbeesKey == `` {
		panic(`CLOUDBEES_KEY empty`)
	}
	c.cloudbeesDevModeKey = os.Getenv(`CLOUDBEES_DEV_MODE_KEY`)
	if c.cloudbeesDevModeKey == `` {
		panic(`CLOUDBEES_DEV_MODE_KEY empty`)
	}
}

func (c *Config) ServiceName() string {
	return os.Getenv(`SERVICE_NAME`)
}

func (c *Config) SchedulerMakeInactiveBanner() string {
	return os.Getenv(`SCHEDULER_BANNER`)
}

func (c *Config) YMSecretCode() string {
	return os.Getenv(`YM_SECRET_CODE`)
}

func (c *Config) Environment() string {
	return os.Getenv(`ENVIRONMENT`)
}

func (c *Config) FrontEndUrl() string {
	frontEndUrl := ``
	switch c.Environment() {
	case `dev-genesis`:
		frontEndUrl = os.Getenv(`FRONT_END_DEV_URL`)
	case `stg-genesis`:
		frontEndUrl = os.Getenv(`FRONT_END_STG_URL`)
	case `prd-genesis`:
		frontEndUrl = os.Getenv(`FRONT_END_PRD_URL`)
	}
	return frontEndUrl
}

func (c *Config) BackEndUrl() string {
	backEndUrl := ``
	switch c.Environment() {
	case `dev-genesis`:
		backEndUrl = os.Getenv(`BACK_END_DEV_URL`)
	case `stg-genesis`:
		backEndUrl = os.Getenv(`BACK_END_STG_URL`)
	case `prd-genesis`:
		backEndUrl = os.Getenv(`BACK_END_PRD_URL`)
	}
	return backEndUrl
}

func (c *Config) DynamoDialector() database.DynamoDialector {
	var d *aws.Config = &aws.Config{
		Region:   aws.String("ap-southeast-1"),
		Endpoint: aws.String("http://localhost:8000"),
	}
	sess := session.Must(session.NewSession(d))
	c.dynamoDielector = database.NewDynamoDialector(sess)

	return c.dynamoDielector
}

func (c *Config) MongoDialector() database.MongoDialector {
	uri := os.Getenv("MONGODB_URI")
	if uri == "" {
		log.Fatal("You must set your 'MONGODB_URI' environmental variable. See\n\t https://docs.mongodb.com/drivers/go/current/usage-examples/#environment-variable")
	}
	credential := options.Credential{
		AuthMechanism: "SCRAM-SHA-256",
		AuthSource:    os.Getenv("MONGODB_AUTH_SOURCE"),
		Username:      os.Getenv("MONGODB_USER"),
		Password:      os.Getenv("MONGODB_PW"),
	}

	sess := options.Client().ApplyURI(uri).SetAuth(credential).SetMonitor(apmmongo.CommandMonitor())

	c.mongoDielector = database.NewMongoDialector("horde", sess)

	return c.mongoDielector
}

// Debug func
func (c *Config) Debug() bool {
	v := os.Getenv("DEBUG")
	c.debug, _ = strconv.ParseBool(v)

	return c.debug
}

// REST func
func (c *Config) REST() bool {
	v := os.Getenv("REST")
	c.rest, _ = strconv.ParseBool(v)

	return c.rest
}

// RPC func
func (c *Config) RPC() bool {
	v := os.Getenv("RPC")
	c.rpc, _ = strconv.ParseBool(v)

	return c.rpc
}

// STREAM func
func (c *Config) STREAM() bool {
	v := os.Getenv("STREAM")
	c.stream, _ = strconv.ParseBool(v)

	return c.stream
}

// Port func
func (c *Config) PublicPort() int {
	v := os.Getenv("PORT")
	c.publicPort, _ = strconv.Atoi(v)

	return c.publicPort
}

// Port func
func (c *Config) PrivatePort() int {
	v := os.Getenv("PRIVATE_PORT")
	c.privatePort, _ = strconv.Atoi(v)

	return c.privatePort
}

// Redis ..
func (c *Config) Redis() RedisConfig {
	c.redis = RedisConfig{
		Host:     os.Getenv("REDIS_HOST"),
		Port:     os.Getenv("REDIS_PORT"),
		Password: os.Getenv("REDIS_PASSWORD"),
		DB:       os.Getenv("REDIS_DB"),
		TLS:      os.Getenv("REDIS_TLS"),
	}

	return c.redis
}

// InitRedis ..
func (c *Config) InitRedis() {
	client, err := cache.ConnectRedis(os.Getenv("REDIS_DB"))
	if err != nil {
		panic(err)
	}

	clientPromo, err := cache.ConnectRedis(os.Getenv("REDIS_DB_PROMO"))
	if err != nil {
		panic(fmt.Errorf("error connect redis promo: %v", err))
	}

	c.cache = client
	c.cachePromo = clientPromo
}

// Cache ..
func (c *Config) Cache() cache.Client {
	return c.cache
}

// Cache Promo..
func (c *Config) CachePromo() cache.Client {
	return c.cachePromo
}

// ConnectDB func
func (c *Config) ConnectDB() {
	c.db = database.InitGorm()
}

func (c *Config) DisconnectDB() {
	c.db.Close()
}

// ConnectDB func
func (c *Config) ConnectNoSqlDB() {
	c.noSqlDB = database.InitMongoDB()
}

func (c *Config) DisconnectNoSqlDB() {
	c.noSqlDB.Close()
}

// DB func
func (c *Config) DB() database.GormDatabase {
	return c.db
}

// DB func
func (c *Config) NoSqlDB() database.MongoDB {
	return c.noSqlDB
}

func (c *Config) InitOpenSearch() {
	c.openSearchDB = database.InitOpenSearchClient()
}

func (c *Config) OpensearchDB() database.OpenSearch {
	return c.openSearchDB
}

// InitToken ...
func (c *Config) InitToken() {
	temporaryLifetimeStr := os.Getenv("TOKEN_TEMPORARY_LIFETIME")
	temporaryLifetime, err := strconv.Atoi(temporaryLifetimeStr)
	if err != nil {
		logger.Panic(`ENV TOKEN_TEMPORARY_LIFETIME ERROR `, err)
	}

	accessTokenLifetimeStr := os.Getenv("ACCESS_TOKEN_LIFETIME")
	accessTokenLifetime, err := strconv.Atoi(accessTokenLifetimeStr)
	if err != nil {
		logger.Panic(`ENV ACCESS_TOKEN_LIFETIME ERROR `, err)
	}

	deviceMasterTemporaryTokenLifetimeStr := os.Getenv("DEVICE_MASTER_TEMPORARY_TOKEN_LIFETIME")
	deviceMasterTemporaryTokenLifetime, err := time.ParseDuration(deviceMasterTemporaryTokenLifetimeStr)
	if err != nil {
		deviceMasterTemporaryTokenLifetime = 5 * time.Minute
	}

	deviceTokenLifetimeStr := os.Getenv("DEVICE_TOKEN_LIFETIME")
	deviceTokenLifetime, err := time.ParseDuration(deviceTokenLifetimeStr)
	if err != nil {
		deviceTokenLifetime = 5 * time.Minute
	}

	publicKey, err := keys.InitPublicKey()
	if err != nil {
		logger.Panic(err)
	}

	privateKey, err := keys.InitPrivateKey()
	if err != nil {
		logger.Panic(err)
	}

	c.token = TokenConfig{
		Name:                               os.Getenv("TOKEN_NAME"),
		Type:                               os.Getenv("TOKEN_TYPE"),
		TemporaryLifetime:                  time.Duration(temporaryLifetime) * time.Second,
		AccessTokenLifeTime:                time.Duration(accessTokenLifetime) * time.Hour,
		DeviceMasterTemporaryTokenLifetime: deviceMasterTemporaryTokenLifetime,
		DeviceTokenLifetime:                deviceTokenLifetime,
		PrivateKey:                         privateKey,
		PublicKey:                          publicKey,
		DeviceJWTSecret:                    os.Getenv("DEVICE_JWT_SECRET"),
	}
}

// InitTokenItegration ...
func (c *Config) InitTokenItegration() {
	publicKey, err := keys.InitPublicIntegrateKey()
	if err != nil {
		logger.Panic(err)
	}

	privateKey, err := keys.InitIntegratePrivateKey()
	if err != nil {
		logger.Panic(err)
	}

	c.tokenIntegration = TokenIntegrationConfig{
		PrivateKey: privateKey,
		PublicKey:  publicKey,
	}
}

// Token ...
func (c *Config) Token() TokenConfig {
	return c.token
}

// TokenIntegration ...
func (c *Config) TokenIntegration() TokenIntegrationConfig {
	return c.tokenIntegration
}

// InitElastic ...
func (c *Config) InitElastic() {
	var err error
	c.es, err = elastic.NewClient()
	if err != nil {
		logger.E(err)
	}
}

// ES ...
func (c *Config) ES() elastic.ElasticClient {
	return c.es
}

// InitEmail ..
func (c *Config) InitEmail() {
	configMail := email.EmailSendGridConfig{
		EmailAPIKey:   os.Getenv(`EMAIL_API_KEY`),
		EmailHost:     os.Getenv(`EMAIL_URL`),
		EmailEndPoint: os.Getenv(`EMAIL_END_POINT`),
		EmailSource:   os.Getenv(`EMAIL_SOURCE`),
		EmailName:     os.Getenv(`EMAIL_NAME`),
	}
	c.mail = email.NewEmailSendGrid(configMail)
}

// Mail ...
func (c *Config) Mail() email.EmailSendGrid {
	return c.mail
}

func (c *Config) EmailTemplateIDResetPassword() string {
	return os.Getenv(`EMAIL_TEMPLATE_ID_RESET_PASSWORD`)
}

func (c *Config) EmailTemplateIDSendOTP() string {
	return os.Getenv(`EMAIL_TEMPLATE_ID_SEND_OTP`)
}

func (c *Config) EmailTemplateIDNewAccount() string {
	return os.Getenv(`EMAIL_TEMPLATE_ID_NEW_ACCOUNT`)
}

// ElexysActive ...
func (c *Config) ElexysActive() bool {
	active, err := strconv.ParseBool(os.Getenv(`ELEXYS_ACTIVE`))
	if err != nil {
		panic(err)
	}
	return active
}

// EnableClientRateToArchiveTable ...
func (c *Config) EnableClientRateToArchiveTable() bool {
	active, err := strconv.ParseBool(os.Getenv(`ENABLE_CLIENT_RATE_TO_ARCHIVE`))
	if err != nil {
		return false
	}
	return active
}

// HydraURL ...
func (c *Config) HydraURL() string {
	return os.Getenv(`HYDRA_URL`)
}

// GoberURL ...
func (c *Config) GoberURL() string {
	return os.Getenv(`GOBER_URL`)
}

func (c *Config) ApiKeyElexys() string {
	return os.Getenv(`API_KEY_ELEXYS`)
}

func (c *Config) ApiUrlElexys() string {
	return os.Getenv(`API_URL_ELEXYS`)
}

func (c *Config) SikatMaxThresholdGrossWeight() float64 {
	sikatMaxThresholdGrossWeight, err := strconv.ParseFloat(os.Getenv(`SIKAT_MAX_THRESHOLD_GROSS`), 64)
	if err != nil {
		logger.Ef("Config-SikatMaxThresholdGrossWeight Error %v", err)
		return 3
	}

	return sikatMaxThresholdGrossWeight
}

// Default Limit Pagination
func (c *Config) DefaultLimitPagination() int {
	defaultLimitPagination, err := strconv.Atoi(os.Getenv(`DEFAULT_LIMIT_PAGINATION`))
	if err != nil {
		return 5
	}

	return defaultLimitPagination
}

func (c *Config) DefaultLifeTimePermissionExpired() int {
	exp, err := strconv.Atoi(os.Getenv(`DEFAULT_LIFE_TIME_PERMISSION_EXP`))
	if err != nil {
		return 6
	}

	return exp
}

func (c *Config) EnableStoreNoSQL() bool {
	active, err := strconv.ParseBool(os.Getenv(`ENABLE_STORE_NOSQL`))
	if err != nil {
		return false
	}
	return active
}

func (c *Config) AccessTokenLifetimeIntegration() int {
	exp, err := strconv.Atoi(os.Getenv(`ACCESS_TOKEN_LIFETIME_INTEGRATION`))
	if err != nil {
		return 1
	}

	return exp
}

func (c *Config) EnableNewTariffBigPack() bool {
	active, err := strconv.ParseBool(os.Getenv(`ENABLE_NEW_TARIFF_BIGPACK`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) EnableBackFillCommoditySurchargeTier() bool {
	active, err := strconv.ParseBool(os.Getenv(`BACKFILL_COMMODITY_SURCHARGE_TIER`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) LimitQueryDBByScheduler() int {
	i, err := strconv.Atoi(os.Getenv(`LIMIT_QUERY_DB_BY_SCHEDULER`))
	if err != nil || i == 0 {
		return 100
	}

	return i
}

func (c *Config) InitGoogleCredential() {
	credential := &GoogleCredential{}

	file, err := ioutil.ReadFile(keys.GoogleKey())
	if err != nil {
		logger.Panic(err)
	}

	err = json.Unmarshal(file, credential)
	if err != nil {
		logger.Panic(err)
	}

	c.googleCredential = credential
}

func (c *Config) GoogleCredential() *GoogleCredential {
	return c.googleCredential
}

func (c *Config) InitPubsub() {
	c.pubsub.GCP = pubsub.NewPubsub(c.googleCredential.ProjectID, keys.GoogleKey())
	// ...pubsub.aws
}

func (c *Config) InitBasicAuth() {
	c.usernameBasicAuth = os.Getenv(`USERNAME_BASIC_AUTHENTICATION`)
	c.passwordBasicAuth = os.Getenv(`PASSWORD_BASIC_AUTHENTICATION`)
}

func (c *Config) PubSub() PubSub {
	return c.pubsub
}

func (c *Config) HydraProgressiveCommissionTopicID() string {
	s := os.Getenv(`HYDRA_PROGRESSIVE_COMMISSION_TOPIC_ID`)
	if s == `` {
		return `hydra_progressive_commission`
	}
	return s
}

func (c *Config) SchedulerClientID() string {
	return os.Getenv(`SCHEDULER_CLIENT_ID`)
}

func (c *Config) SchedulerClientSecret() string {
	return os.Getenv(`SCHEDULER_CLIENT_SECRET`)
}

func (c *Config) GetCpcDayOfTheMonthSchedule() int {
	dayOfTheMonth, err := strconv.Atoi(os.Getenv(`CPC_DAY_OF_MONTH_SCHEDULE`))
	if err != nil || dayOfTheMonth == 0 {
		return 8
	}

	return dayOfTheMonth
}

func (c *Config) GetDistrictClientDefault() int {
	districtClient, err := strconv.Atoi(os.Getenv(`DISTRICT_CLIENT_DEFAULT`))
	if err != nil {
		return 0
	}

	return districtClient
}

func (c *Config) GetBasicAuthUsername() string {
	return c.usernameBasicAuth
}

func (c *Config) GetBasicAuthPassword() string {
	return c.passwordBasicAuth
}

func (c *Config) ALGOURL() string {
	return os.Getenv(`ALGO_URL`)
}

func (c *Config) OdooURL() string {
	return os.Getenv(`ODOO_URL`)
}

func (c *Config) OdooApiKey() string {
	return os.Getenv(`ODOO_API_KEY`)
}

func (c *Config) AlgoAuthUsername() string {
	return os.Getenv(`ALGO_AUTH_USERNAME`)
}

func (c *Config) AlgoAuthPassword() string {
	return os.Getenv(`ALGO_AUTH_PASSWORD`)
}

func (c *Config) AlgoAuthRole() string {
	return os.Getenv(`ALGO_AUTH_ROLE`)
}

func (c *Config) AlgoVehicleAuthUsername() string {
	return os.Getenv(`ALGO_VEHICLE_AUTH_USERNAME`)
}

func (c *Config) AlgoVehicleAuthPassword() string {
	return os.Getenv(`ALGO_VEHICLE_AUTH_PASSWORD`)
}

func (c *Config) AlgoVehicleAuthRole() string {
	return os.Getenv(`ALGO_VEHICLE_AUTH_ROLE`)
}

func (c *Config) LibertaAuthUsername() string {
	return os.Getenv(`LIBERTA_AUTH_USERNAME`)
}

func (c *Config) LibertaAuthPassword() string {
	return os.Getenv(`LIBERTA_AUTH_PASSWORD`)
}

func (c *Config) LibertaAuthRole() string {
	return os.Getenv(`LIBERTA_AUTH_ROLE`)
}

func (c *Config) EmailNotifRateVersionExp() string {
	return os.Getenv(`EMAIL_NOTIF_RATE_VERSION_EXP`)
}
func (c *Config) SignatureNotifRateVersionExp() string {
	return os.Getenv(`SIGNATURE_NOTIF_RATE_VERSION_EXP`)
}

// EnableBatchUploadRate ...
func (c *Config) EnableBatchUploadRate() bool {
	active, err := strconv.ParseBool(os.Getenv(`ENABLE_BATCH_UPLOAD_RATE`))
	if err != nil {
		return false
	}
	return active
}

func (c *Config) CustomUploadRateBatchRowCount() int {
	batchCount, err := strconv.Atoi(os.Getenv(`CUSTOM_UPLOAD_RATE_BATCH_ROW_COUNT`))
	if err != nil || batchCount <= 0 {
		return 10000
	}
	return batchCount
}

func (c *Config) CustomUploadRateBatchMinimumRow() int {
	minRow, err := strconv.Atoi(os.Getenv(`CUSTOM_UPLOAD_RATE_BATCH_MIN_ROW`))
	if err != nil {
		return 1000
	}
	return minRow
}

func (c *Config) DefaultBatchEventLimit() int {
	limit, err := strconv.Atoi(os.Getenv(`DEFAULT_BATCH_EVENT_LIMIT`))
	if err != nil || limit <= 0 {
		return 1
	}

	return limit
}

func (c *Config) DefaultBatchEventRetryLimit() int {
	limit, err := strconv.Atoi(os.Getenv(`DEFAULT_BATCH_EVENT_RETRY_LIMIT`))
	if err != nil {
		return 3
	}

	return limit
}

func (c *Config) HordeEventPullWorkerTopicID() string {
	s := os.Getenv(`HORDE_EVENT_PULL_WORKER_TOPIC_ID`)
	if s == `` {
		return `horde_event_pull_worker`
	}
	return s
}

func (c *Config) BatchUploadRateCacheDuration() int {
	d, err := strconv.Atoi(os.Getenv(`BATCH_UPLOAD_RATE_CACHE_DURATION`))
	if err != nil || d < 0 {
		return 30
	}
	return d
}

func (c *Config) EnableSchedulerRateActiveDate() bool {
	ok, err := strconv.ParseBool(os.Getenv(`ENABLE_SCHEDULER_RATE_ACTIVE_DATE`))
	if err != nil {
		return false
	}
	return ok
}

func (c *Config) InitFlagManagementCloudBees() {
	c.flagManagement = flag_management.InitFlagManagementCloudBees()
}

func (c *Config) FlagManagement() *flag_management.FlagsModel {
	return c.flagManagement
}

func (c *Config) PegasusURL() string {
	return os.Getenv(`PEGASUS_URL`)
}

func (c *Config) PegasusAuthUsername() string {
	return os.Getenv(`PEGASUS_AUTH_USERNAME`)
}

func (c *Config) PegasusAuthPassword() string {
	return os.Getenv(`PEGASUS_AUTH_PASSWORD`)
}

func (c *Config) PegasusBasicAuth() string {
	username := os.Getenv(`PEGASUS_BASIC_AUTH_USERNAME`)
	password := os.Getenv(`PEGASUS_BASIC_AUTH_PASSWORD`)
	return crypto.GenerateBasicAuthHeader(username, password)
}

func (c *Config) NewCalcVolumeWeightBigPackDate() time.Time {
	startDate, err := utils.ParseUTC7("2006-01-02", os.Getenv(`NEW_CALCULATE_VOLUME_WEIGHT_BIGPACK_DATE`))
	if err != nil || startDate.IsZero() {
		startDate, _ = utils.ParseUTC7("2006-01-02", `2023-11-03`)
	}

	return startDate
}

func (c *Config) NewCalcVolumeWeightBigPackDivider() int {
	divider, err := strconv.Atoi(os.Getenv(`NEW_CALCULATE_VOLUME_WEIGHT_BIGPACK_DIVIDER`))
	if err != nil {
		return 6000
	}

	return divider
}

func (c *Config) TransportTypeConfigUseLimit() int {
	divider, err := strconv.Atoi(os.Getenv(`TRANSPORT_TYPE_USE_LIMIT`))
	if err != nil {
		return 100
	}
	return divider
}

func (c *Config) TransportTypeConfigUseGrouping() bool {
	active, err := strconv.ParseBool(os.Getenv(`TRANSPORT_TYPE_USE_GROUPING`))
	if err != nil {
		return false
	}
	return active
}

func (c *Config) MetabaseURL() string {
	return os.Getenv(`METABASE_URL`)
}

func (c *Config) MetabaseAuthUsername() string {
	return os.Getenv(`METABASE_AUTH_USERNAME`)
}

func (c *Config) EnableTariffDispatcher() bool {
	enableTariffDispatcherString := os.Getenv(`ENABLE_TARIFF_DISPATCHER`)
	enableTariffDispatcher, _ := strconv.ParseBool(enableTariffDispatcherString)
	return enableTariffDispatcher
}

func (c *Config) MetabaseAuthPassword() string {
	return os.Getenv(`METABASE_AUTH_PASSWORD`)
}

func (c *Config) HydraCreatePickupCorporateTopicID() string {
	// hydra_create_pickup_corporate
	s := os.Getenv(`HYDRA_CREATE_PICKUP_CORPORATE_TOPIC_ID`)
	if s == `` {
		return `hydra_create_pickup_corporate`
	}
	return s
}

func (c *Config) HydraCreatePickupCorporateWorkers() int {
	wokers, err := strconv.Atoi(os.Getenv(`HYDRA_CREATE_PICKUP_CORPORATE_WORKERS`))
	if err != nil {
		return 10
	}
	return wokers
}

func (c *Config) MaxPickupCorporateAddress() int {
	val, err := strconv.Atoi(os.Getenv(`MAX_PICKUP_CORPORATE_ADDRESS`))
	if err != nil {
		return 10
	}
	return val
}

func (c *Config) LimitAccountNotification() int {
	val, err := strconv.Atoi(os.Getenv(`LIMIT_ACCOUNT_NOTIFICATION`))
	if err != nil {
		return 20
	}
	return val
}

func (c *Config) LimitListAccountNotification() int {
	val, err := strconv.Atoi(os.Getenv(`LIMIT_LIST_ACCOUNT_NOTIFICATION`))
	if err != nil {
		return 300
	}
	return val
}

func (c *Config) UseMysqlInCustomerV2() bool {
	isUseMysqlStr := strings.TrimSpace(os.Getenv(`USE_MYSQL_IN_CUSTOMER_V2`))
	isUseMysql, _ := strconv.ParseBool(isUseMysqlStr)
	return isUseMysql
}

func (c *Config) GetKeyEncryptionDB() []byte {
	str := strings.TrimSpace(os.Getenv(`KEY_ENCRYPTION_DATABASE`))
	if str == `` {
		return []byte("")
	}
	return []byte(str)
}

func (c *Config) UseNewTokenVerification() bool {
	str := strings.TrimSpace(os.Getenv(`USE_NEW_TOKEN_VERIFICATION`))
	res, _ := strconv.ParseBool(str)
	return res
}

func (c *Config) GetLimitedAssigned3LCRule() string { return os.Getenv(`LIMITED_ASSIGNED_3LC_RULE`) }

func (c *Config) OpenSearchEnable() bool {
	active, err := strconv.ParseBool(os.Getenv(`OPENSEARCH_ENABLE`))
	if err != nil {
		return false
	}
	return active
}

func (c *Config) GetTransactionCodes() map[string]bool {
	list := os.Getenv(`TRANSACTION_CODES`)
	mapData := make(map[string]bool)
	for _, item := range strings.Split(list, ",") {
		mapData[item] = true
	}
	return mapData
}
func (c *Config) GetLogFailClientRateTTL() time.Duration {
	cityTTLStr := os.Getenv("BULK_LOG_FAIL_CLIENT_RATE_TTL")
	cityTTL, err := strconv.Atoi(cityTTLStr)
	if err != nil {
		logger.Panic(`ENV BULK_LOG_FAIL_CLIENT_RATE_TTL ERROR `, err)
	}
	return time.Duration(cityTTL) * time.Hour
}

func (c *Config) EnableUploadStuck() bool {
	active, err := strconv.ParseBool(os.Getenv(`ENABLE_UPLOAD_STUCK`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) NotificationEventBatchLimit() int {
	limit, err := strconv.Atoi(os.Getenv(`NOTIFICATION_BATCH_EVENT_LIMIT`))
	if err != nil || limit <= 0 {
		return 1
	}

	return limit
}

func (c *Config) BulkBookingMaxRow() int {
	row, _ := strconv.Atoi(os.Getenv(`BULK_BOOKING_MAX_ROW`))
	if row < 1 {
		return 500
	}
	return row
}

func (c *Config) BulkBookingMaxQuota() int {
	quota, _ := strconv.Atoi(os.Getenv(`BULK_BOOKING_MAX_QUOTA`))
	if quota < 1 {
		return 1000
	}
	return quota
}

func (c *Config) BulkBookingProcessTimeoutSecond() int {
	timeout, _ := strconv.Atoi(os.Getenv(`BULK_BOOKING_PROCESS_TIMEOUT_SECOND`))
	if timeout < 1 {
		return 3600
	}
	return timeout
}

func (c *Config) BulkBookingMaxSizeMB() int {
	size, _ := strconv.Atoi(os.Getenv(`BULK_BOOKING_MAX_SIZE_MB`))
	if size < 1 {
		return 5
	}
	return size
}

func (c *Config) CacheTimeLimitProfile() int {
	timelimit, _ := strconv.Atoi(os.Getenv(`CACHE_TIMELIMIT_PROFILE`))
	if timelimit < 1 {
		return 1
	}
	return timelimit
}

func (c *Config) OTPValidTimeLimit() int {
	size, _ := strconv.Atoi(os.Getenv(`OTP_VALID_TIME_LIMIT`))
	if size < 1 {
		// default 2 minunte (on second)
		return 120
	}
	return size
}

func (c *Config) OTPValidTimeExpired() int {
	size, _ := strconv.Atoi(os.Getenv(`OTP_VALID_TIME_EXPIRED`))
	if size < 1 {
		// default 3 hours
		return 10800
	}
	return size
}

func (c *Config) RecaptchaSecretKey() string {
	return os.Getenv("GOOGLE_RECAPTCHA_SECRET_KEY")
}

func (c *Config) MessagingSenderURL() string {
	return os.Getenv("MESSAGING_SENDER_URL")
}

func (c *Config) MessagingSenderUID() string {
	return os.Getenv("MESSAGING_SENDER_UID")
}

func (c *Config) MessagingSenderID() string {
	return os.Getenv("MESSAGING_SENDER_ID")
}

func (c *Config) MessagingSenderUsername() string {
	return os.Getenv("MESSAGING_SENDER_USERNAME")
}

func (c *Config) MessagingSenderPassword() string {
	return os.Getenv("MESSAGING_SENDER_PASSWORD")
}

func (c *Config) MessagingSenderMessageText() string {
	return `Hi {{.Username}}, 

Kode verifikasi untuk atur ulang password: {{.OTPCode}}

Demi keamanan, jangan bagikan kode ini. OTP berlaku {{ .Hour }} jam.`
}

func (c *Config) MessagingSubjectResetPassword() string {
	return os.Getenv("MESSAGING_SUBJECT_RESET_PASSWORD")
}

func (c *Config) MessagingSenderMessageTextV2() string {
	return `Hai {{.Username}},

Kode verifikasi untuk {{.Action}}: {{.OTPCode}}

Demi keamanan, jangan bagikan kode ini. OTP berlaku {{ .Hour }} jam.`
}

func (c *Config) EmailTemplateIDVerification() string {
	return os.Getenv(`EMAIL_TEMPLATE_ID_VERIFICATION`)
}

func (c *Config) MessagingSubjectWithdrawSaldo() string {
	return os.Getenv("MESSAGING_SUBJECT_WITHDRAW_SALDO")
}

func (c *Config) MessagingSubjectAdjustSaldo() string {
	return os.Getenv("MESSAGING_SUBJECT_ADJUST_SALDO")
}

func (c *Config) MessagingSubjectChangePassword() string {
	return os.Getenv("MESSAGING_SUBJECT_CHANGE_PASSWORD")
}

func (c *Config) MessagingTitleWithdrawSaldo() string {
	return os.Getenv("MESSAGING_TITLE_WITHDRAW_SALDO")
}

func (c *Config) MessagingTitleAdjustSaldo() string {
	return os.Getenv("MESSAGING_TITLE_ADJUST_SALDO")
}

func (c *Config) MessagingTitleChangePassword() string {
	return os.Getenv("MESSAGING_TITLE_CHANGE_PASSWORD")
}

func (c *Config) MessagingDescWithdrawSaldo() string {
	return os.Getenv("MESSAGING_DESC_WITHDRAW_SALDO")
}

func (c *Config) MessagingDescAdjustSaldo() string {
	return os.Getenv("MESSAGING_DESC_ADJUST_SALDO")
}
func (c *Config) MessagingDescChangePassword() string {
	return os.Getenv("MESSAGING_DESC_CHANGE_PASSWORD")
}

func (c *Config) RecaptchaSecretKeyV3() string {
	return os.Getenv("GOOGLE_RECAPTCHA_SECRET_KEY_V3")
}

func (c *Config) HelpdeskPhoneNumber() string {
	phone := os.Getenv("HELPDESK_PHONE_NUMBER")
	if phone == "" {
		return "0851-8076-3985"
	}
	return phone
}

func (c *Config) MaxPOSDeviceMasterPerAccount() int {
	maxDevice, err := strconv.Atoi(os.Getenv("MAX_POS_DEVICE_MASTER_PER_ACCOUNT"))
	if err != nil || maxDevice <= 0 {
		return 2
	}
	return maxDevice
}

func (c *Config) MaxConsoleDeviceMasterPerAccount() int {
	maxDevice, err := strconv.Atoi(os.Getenv("MAX_CONSOLE_DEVICE_MASTER_PER_ACCOUNT"))
	if err != nil || maxDevice <= 0 {
		return 4
	}
	return maxDevice
}

func (c *Config) InitDistributedLock() {
	rs, err := cache.NewDistributedLock()
	if err != nil {
		logger.Panic(err)
	}
	c.distributedLock = rs
}
func (c *Config) DistributedLock() *cache.DistributedLock {
	if c.distributedLock == nil {
		c.InitDistributedLock()
	}
	return c.distributedLock
}

func (c *Config) GetDeviceEncryptionSecretKey() string {
	secretKey := os.Getenv("DEVICE_ENCRYPTION_SECRET_KEY")
	if secretKey == "" {
		logger.Panic("DEVICE_ENCRYPTION_SECRET_KEY is not set")
	}
	return secretKey
}

// RecaptchaMobileProjectID returns the project ID for mobile reCAPTCHA Enterprise
func (c *Config) RecaptchaMobileProjectID() string {
	return os.Getenv("GOOGLE_RECAPTCHA_MOBILE_PROJECT_ID")
}

// RecaptchaMobileSiteKey returns the site key for mobile reCAPTCHA Enterprise
func (c *Config) RecaptchaMobileSiteKey() string {
	return os.Getenv("GOOGLE_RECAPTCHA_MOBILE_SITE_KEY")
}

func (c *Config) OpensearchViewDistrictV3Enable() bool {
	active, _ := strconv.ParseBool(os.Getenv(`OPENSEARCH_VIEW_DISTRICT_V3_ENABLE`))
	return active
}
