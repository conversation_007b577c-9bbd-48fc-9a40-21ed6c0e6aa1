package pubsub

import (
	"context"
	"fmt"
	"log"

	"cloud.google.com/go/pubsub"
	"github.com/Lionparcel/horde/shared/logger"
	"google.golang.org/api/option"
)

type PubSubMessage struct {
	Attributes            map[string]string
	Message               string
	OrderingKey           string
	EnableMessageOrdering bool
}

type GooglePubsub interface {
	PublishMessage(ctx context.Context, topicID string, data *PubSubMessage) (messageID string, err error)
	PullMessage(ctx context.Context, subscriptionID string, settings *pubsub.ReceiveSettings, f func(ctx context.Context, m *pubsub.Message)) error
}

type pubsubCtx struct {
	Client *pubsub.Client
}

func NewPubsub(projectID, credential string) GooglePubsub {
	client, err := pubsub.NewClient(context.Background(), projectID, option.WithCredentialsFile(credential))
	if err != nil {
		log.Fatal(fmt.Errorf(`pubsub init new client: %s`, err.Error()))
	}

	pubsubCtx := pubsubCtx{
		Client: client,
	}

	return &pubsubCtx
}

// PublishMessage
// Attention !!! Please read the comment
// If you call this function it will be block your code, until the result is returned and a server-generated
// messageID is returned for the published message, or until ctx is set done.
func (c *pubsubCtx) PublishMessage(ctx context.Context, topicID string, data *PubSubMessage) (messageID string, err error) {
	topic := c.Client.Topic(topicID)

	if data.EnableMessageOrdering {
		// enabling topic to be able push message with ordering
		// ensure that the subcriber is allow message order too
		topic.EnableMessageOrdering = true
	}

	result := topic.Publish(ctx, &pubsub.Message{
		Data:        []byte(data.Message),
		Attributes:  data.Attributes,
		OrderingKey: data.OrderingKey,
	})

	// Block until the result is returned and a server-generated
	// messageID is returned for the published message.
	messageID, err = result.Get(ctx)
	if err != nil {
		logger.Ef(`PubSub-PublishMessage Error %s`, err.Error())
		return "", err
	}

	return messageID, nil
}

// PullMessage
// Attention !!! Please read the comment
// If you call this function it will be block your code, until ctx is set done
func (c *pubsubCtx) PullMessage(ctx context.Context, subscriptionID string, settings *pubsub.ReceiveSettings, f func(ctx context.Context, m *pubsub.Message)) error {
	sub := c.Client.Subscription(subscriptionID)

	sub.ReceiveSettings = *settings

	err := sub.Receive(ctx, f)
	if err != nil {
		logger.Ef(`PubSub-PullMessage Error %s`, err.Error())
		return err
	}
	return nil
}
