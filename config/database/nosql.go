package database

import (
	"context"
	"strings"

	"github.com/labstack/gommon/log"
)

const (
	DYNAMODB = `dynamo`
	MONGODB  = `mongo`
)

type INoSQL interface {
	Limit(i int) INoSQL
	Offset(i int) INoSQL
	Scan(dest interface{})
	Model(modelData interface{}) INoSQL
	Where(field string, value interface{}) INoSQL
	WhereLike(field string, value string) INoSQL
	Find(ctx context.Context, tableName string) INoSQL
	FindResult() []interface{}
	Create(ctx context.Context, in interface{}) INoSQL
	Execute() error
}

func NewNoSqlDatabase(typeDB string, conn interface{}) INoSQL {
	defer func() {
		if r := recover(); r != nil {
			log.Error(r)
		}
	}()

	if strings.EqualFold(typeDB, DYNAMODB) {
		con := conn.(DynamoDialector)
		return NewDynamoDB(con)
	}

	if strings.EqualFold(typeDB, MONGODB) {
		con := conn.(MongoDialector)
		return NewMongoDB(con)
	}

	return nil
}
