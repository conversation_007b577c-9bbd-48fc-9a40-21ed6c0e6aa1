package database

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
	"github.com/aws/aws-sdk-go/service/dynamodb/expression"
	"github.com/labstack/gommon/log"
)

const (
	CREATE_LIFECYCLE = `create`
)

type DynamoDialector struct {
	sess *session.Session
}

func NewDynamoDialector(sess *session.Session) DynamoDialector {
	return DynamoDialector{sess: sess}
}

func (c *DynamoDialector) Initialize(conn *dynamoDB) {
	cc := dynamodb.New(c.sess)
	conn.svc = cc
}

type dynamoDB struct {
	svc              *dynamodb.DynamoDB
	input            *dynamodb.PutItemInput
	lifeCycle        string
	conditionBuilder expression.ConditionBuilder
	cacheStore       *sync.Map
}

func NewDynamoDB(sess DynamoDialector) INoSQL {
	conn := new(dynamoDB)
	conn.cacheStore = new(sync.Map)

	sess.Initialize(conn)

	return conn
}

func (c *dynamoDB) Execute() error {

	if strings.EqualFold(c.lifeCycle, ``) {
		return errors.New("no action")
	}

	if strings.EqualFold(c.lifeCycle, CREATE_LIFECYCLE) {
		_, err := c.svc.PutItem(c.input)
		if err != nil {
			return errors.New("dynamodb ERR: " + err.Error())
		}
	}

	return nil
}

func (c *dynamoDB) Create(ctx context.Context, in interface{}) INoSQL {
	defer func() {
		if r := recover(); r != nil {
			log.Error(r)
		}
	}()
	c.input = nil
	c.lifeCycle = ``
	item, err := dynamodbattribute.MarshalMap(in)
	if err != nil {
		log.Error(err)
	}

	if reflect.ValueOf(in).Kind() != reflect.Ptr {
		panic("must pointer")
	}

	st := reflect.ValueOf(in).MethodByName("TableName").Call([]reflect.Value{})
	if len(st) < 1 {
		panic("in not implement TableName")
	}
	tableName := st[0].String()

	c.input = &dynamodb.PutItemInput{
		Item:      item,
		TableName: aws.String(tableName),
	}

	c.lifeCycle = CREATE_LIFECYCLE
	return c
}

func (c *dynamoDB) Model(modelData interface{}) INoSQL {
	dataType := reflect.ValueOf(modelData).Kind()
	if dataType != reflect.Ptr {
		panic("must using pointer")
	}

	c.cacheStore.Store("model", modelData)

	return c
}

func (c *dynamoDB) Scan(dest interface{}) {
	defer func() {
		//reset cache
		c.cacheStore = new(sync.Map)
		c.conditionBuilder = expression.ConditionBuilder{}
	}()

	dataType := reflect.ValueOf(dest)
	if dataType.Kind() != reflect.Ptr {
		panic("must using pointer")
	}
	if dataType.Elem().Kind() != reflect.Slice {
		panic("dest must be slice")
	}

	params := &dynamodb.ScanInput{}
	builder := expression.NewBuilder()
	if v, ok := c.cacheStore.Load("builder"); ok && v.(bool) {
		builder = builder.WithFilter(c.conditionBuilder)
		expr, err := builder.Build()
		if err != nil {
			panic("dynamodb ERR: " + err.Error())
		}
		params.ExpressionAttributeNames = expr.Names()
		params.ExpressionAttributeValues = expr.Values()
		params.FilterExpression = expr.Filter()
		params.ProjectionExpression = expr.Projection()
	}

	val, ok := c.cacheStore.Load("model")
	if !ok || val == nil {
		panic("model must be fill first")
	}
	st := reflect.ValueOf(val).MethodByName("TableName")
	if !st.IsValid() {
		panic("model not implement TableName")
	}
	stt := st.Call([]reflect.Value{})
	tableName := stt[0].String()

	params.TableName = aws.String(tableName)

	datas := reflect.ValueOf(dest)
	datas = datas.Elem()
	data := datas.Type().Elem()

	result, err := c.svc.Scan(params)
	if err != nil {
		panic("dynamodb ERR: " + err.Error())
	}

	for _, i := range result.Items {
		var item interface{}

		err = dynamodbattribute.UnmarshalMap(i, &item)
		if err != nil {
			panic("dynamodb ERR: " + err.Error())
		}

		dt := item.(map[string]interface{})
		inData := reflect.New(data)
		structValue := reflect.ValueOf(inData.Interface()).Elem()

		fieldName := map[string]int{}
		for i := 0; i < structValue.NumField(); i++ {
			structTag := structValue.Type().Field(i)
			tag := structTag.Tag

			if val, ok := tag.Lookup("json"); ok {
				key := strings.Split(val, ",")[0]
				fieldName[key] = i
			}
		}

		for k, v := range dt {
			structFieldValue := structValue.Field(fieldName[k])

			if !structFieldValue.IsValid() {
				log.Errorf("No such field: %s in obj", k)
				continue
			}

			if !structFieldValue.CanSet() {
				log.Errorf("Cannot set %s field value", k)
				continue
			}

			structFieldType := structFieldValue.Type()
			val := reflect.ValueOf(v)
			if structFieldType != val.Type() {
				timeFormat := reflect.ValueOf(time.Time{}).Type()
				if structFieldType == timeFormat {
					if val.Kind() == reflect.String {
						t, err := time.Parse(time.RFC3339Nano, v.(string))
						if err == nil {
							val = reflect.ValueOf(t)
							structFieldValue.Set(val)
							continue
						}
					}
				}
				if val.Type().ConvertibleTo(structFieldType) {
					val = val.Convert(structFieldType)
					structFieldValue.Set(val)
					continue
				}

				log.Print("Provided value type didn't match obj field type")
				continue
			}

			structFieldValue.Set(val)
		}

		datas.Set(reflect.Append(datas, reflect.ValueOf(inData.Elem().Interface())))
	}

}

func (c *dynamoDB) FindResult() []interface{} {
	defer func() {
		//reset cache
		c.cacheStore = new(sync.Map)
		c.conditionBuilder = expression.ConditionBuilder{}
	}()

	res := make([]interface{}, 0)

	params := &dynamodb.ScanInput{}
	builder := expression.NewBuilder()
	if v, ok := c.cacheStore.Load("builder"); ok && v.(bool) {
		builder = builder.WithFilter(c.conditionBuilder)
		expr, err := builder.Build()
		if err != nil {
			log.Error(err)
		}
		params.ExpressionAttributeNames = expr.Names()
		params.ExpressionAttributeValues = expr.Values()
		params.FilterExpression = expr.Filter()
		params.ProjectionExpression = expr.Projection()
	}
	val, _ := c.cacheStore.Load("model")
	if reflect.ValueOf(val).Kind() != reflect.Ptr {
		panic("must pointer")
	}

	st := reflect.ValueOf(val).MethodByName("TableName").Call([]reflect.Value{})
	if len(st) < 1 {
		panic("in not implement TableName")
	}
	tableName := st[0].String()

	params.TableName = aws.String(tableName)

	result, err := c.svc.Scan(params)
	if err != nil {
		panic("dynamodb ERR: " + err.Error())
	}

	for _, v := range result.Items {
		res = append(res, v)
	}

	return res
}

func (c *dynamoDB) Find(ctx context.Context, tableName string) INoSQL {
	return c
}

func (c *dynamoDB) Where(field string, value interface{}) INoSQL {
	if v, ok := c.cacheStore.Load("builder"); ok && v.(bool) {
		c.conditionBuilder = c.conditionBuilder.And(expression.Name(field).Equal(expression.Value(value)))
		return c
	}
	c.conditionBuilder = expression.Name(field).Equal(expression.Value(value))

	c.cacheStore.Store("builder", true)
	return c
}

func (c *dynamoDB) WhereLike(field string, value string) INoSQL {
	val := fmt.Sprintf("%v", value)
	if v, ok := c.cacheStore.Load("builder"); ok && v.(bool) {
		c.conditionBuilder = c.conditionBuilder.And(expression.Name(field).Contains(val))
		return c
	}
	c.conditionBuilder = expression.Name(field).Contains(val)

	c.cacheStore.Store("builder", true)
	return c
}

func (c *dynamoDB) Limit(i int) INoSQL {
	return c
}

func (c *dynamoDB) Offset(i int) INoSQL {
	return c
}
