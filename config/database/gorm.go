package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	logger2 "github.com/Lionparcel/horde/shared/logger"
	"go.elastic.co/apm/module/apmsql"
	_ "go.elastic.co/apm/module/apmsql/mysql"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type gormInstance struct {
	master, slave, clone, cloneBL, checkRepLag *gorm.DB
}

// Master initialize DB for master data
func (g *gormInstance) Master() *gorm.DB {
	return g.master
}

func (g *gormInstance) Slave() *gorm.DB {
	return g.slave
}

func (g *gormInstance) Clone() *gorm.DB {
	return g.clone
}

func (g *gormInstance) CloneBL() *gorm.DB {
	return g.cloneBL
}

func (g *gormInstance) CheckRepLag() *gorm.DB {
	return g.checkRepLag
}

func (g *gormInstance) Close() {
	closeDB(g.master)
	closeDB(g.slave)
	closeDB(g.clone)
	closeDB(g.cloneBL)
	closeDB(g.checkRepLag)
}

func closeDB(db *gorm.DB) {
	sqlDB, err := db.DB()
	if err != nil {
		logger2.E(err)
		return
	}
	if err := sqlDB.Close(); err != nil {
		logger2.E(err)
	}
}

// GormDatabase abstraction
type GormDatabase interface {
	Master() *gorm.DB
	Slave() *gorm.DB
	Clone() *gorm.DB
	CloneBL() *gorm.DB
	CheckRepLag() *gorm.DB
	Close()
}

// InitGorm ...
func InitGorm() GormDatabase {
	inst := new(gormInstance)

	dbLogger := createLogger()

	gormConfig := createGormConfig(dbLogger)

	inst.master = initDBConnection("DB_MASTER", "mysql", gormConfig)
	inst.slave = initDBConnection("DB_SLAVE", "mysql", gormConfig)
	inst.clone = initDBConnection("DB_CLONE", "mysql", gormConfig)
	inst.cloneBL = initDBConnection("DB_CLONE_BL", "mysql", gormConfig)
	inst.checkRepLag = initDBConnection("DB_SLAVE_CHECK_LAG", "mysql", gormConfig)

	return inst
}

func createLogger() logger.Interface {
	return logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold: time.Second,   // Slow SQL threshold
			LogLevel:      logger.Silent, // Log level
			Colorful:      true,          // Enable color
		},
	)
}

func createGormConfig(dbLogger logger.Interface) *gorm.Config {
	return &gorm.Config{
		PrepareStmt:            true,
		SkipDefaultTransaction: true,
		Logger:                 dbLogger,
	}
}

func initDBConnection(envPrefix, dbType string, gormConfig *gorm.Config) *gorm.DB {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Asia%%2FJakarta&charset=utf8",
		os.Getenv(envPrefix+"_USERNAME"), os.Getenv(envPrefix+"_PASSWORD"),
		os.Getenv(envPrefix+"_HOST"), os.Getenv(envPrefix+"_PORT"),
		os.Getenv(envPrefix+"_NAME"))

	sqlDB, err := apmsql.Open(dbType, dsn)
	if err != nil {
		logger2.Panic(err)
	}

	configureDB(sqlDB)

	db, err := gorm.Open(selectDialector(dbType, sqlDB), gormConfig)
	if err != nil {
		logger2.Panic(err)
	}

	return db
}

func selectDialector(dbType string, sqlDB *sql.DB) gorm.Dialector {
	switch dbType {
	case "mysql":
		return mysql.New(mysql.Config{Conn: sqlDB})
	// case "postgres":
	default:
		logger2.Panic(fmt.Sprintf("unsupported db type: %s", dbType))
		return nil
	}
}

func configureDB(sqlDB *sql.DB) {
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Minute)
}
