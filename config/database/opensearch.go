package database

import (
	"os"

	"github.com/Lionparcel/horde/shared/logger"
	"github.com/opensearch-project/opensearch-go"
)

type openSearchInstance struct {
	master *opensearch.Client
}

func (c *openSearchInstance) Master() *opensearch.Client {
	return c.master
}

type OpenSearch interface {
	Master() *opensearch.Client
}

func InitOpenSearchClient() OpenSearch {
	config := opensearch.Config{
		Addresses: []string{
			os.Getenv("OPENSEARCH_URL"),
		},
		Username: os.Getenv("OPENSEARCH_USERNAME"),
		Password: os.Getenv("OPENSEARCH_PASSWORD"),
	}
	client, err := opensearch.NewClient(config)
	if err != nil {
		logger.Panic("Error creating OpenSearch client: %v", err)
	}

	return &openSearchInstance{
		master: client,
	}
}
