package database

import (
	"context"
	"encoding/json"
	"reflect"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type MongoDialector struct {
	sess   *options.ClientOptions
	dbName string
}

func NewMongoDialector(dbName string, sess *options.ClientOptions) MongoDialector {
	return MongoDialector{
		sess:   sess,
		dbName: dbName,
	}
}

func (c *MongoDialector) Initialize(db *mongoDB) {
	conn, err := mongo.Connect(context.Background(), c.sess)
	if err != nil {
		panic(err)
	}

	err = conn.Ping(context.Background(), nil)
	if err != nil {
		panic(err)
	}

	db.svc = conn.Database(c.dbName)
}

type mongoDB struct {
	svc        *mongo.Database
	cacheStore *sync.Map
	ctx        context.Context
	collection *mongo.Collection
	input      interface{}
	filter     primitive.D
	limit      int
	offset     int
}

func NewMongoDB(sess MongoDialector) INoSQL {
	conn := &mongoDB{}
	conn.cacheStore = new(sync.Map)
	conn.filter = bson.D{}
	sess.Initialize(conn)

	return conn
}

func (c *mongoDB) Model(modelData interface{}) INoSQL {
	c.cacheStore.Store("table", modelData)
	return c
}

func (c *mongoDB) Where(field string, value interface{}) INoSQL {
	c.filter = append(c.filter, bson.E{field, value})
	c.cacheStore.Store("builder", true)
	return c
}
func (c *mongoDB) WhereLike(field string, value string) INoSQL {

	c.filter = append(c.filter, bson.E{field, bson.D{{"$regex", value}}})
	c.cacheStore.Store("builder", true)
	return c
}
func (c *mongoDB) Find(ctx context.Context, tableName string) INoSQL {
	return c
}
func (c *mongoDB) FindResult() []interface{} {
	defer func() {
		c.filter = bson.D{}
	}()

	return nil
}

func (c *mongoDB) Create(ctx context.Context, in interface{}) INoSQL {
	if reflect.ValueOf(in).Kind() != reflect.Ptr {
		panic("must pointer")
	}

	st := reflect.ValueOf(in).MethodByName("TableName")
	if !st.IsValid() {
		panic("in not implement TableName")
	}
	stt := st.Call([]reflect.Value{})

	tableName := stt[0].String()
	c.collection = c.svc.Collection(tableName)
	c.input = in
	c.ctx = ctx
	c.cacheStore.Store("create", true)
	return c
}

func (c *mongoDB) Execute() error {
	if val, ok := c.cacheStore.Load("create"); ok && val.(bool) {
		_, err := c.collection.InsertOne(c.ctx, c.input)
		if err != nil {
			panic("mongodb ERR: " + err.Error())
		}
	}

	c.cacheStore = new(sync.Map)
	return nil
}

func (c *mongoDB) Limit(i int) INoSQL {
	c.limit = i
	return c
}

func (c *mongoDB) Offset(i int) INoSQL {
	c.offset = i
	return c
}

func (c *mongoDB) Scan(dest interface{}) {
	defer func() {
		//reset cache
		c.cacheStore = new(sync.Map)
		c.filter = bson.D{}
		c.limit = 0
		c.offset = 0
	}()

	dataType := reflect.TypeOf(dest)
	if dataType.Kind() != reflect.Ptr {
		panic("must using pointer")
	}
	if dataType.Elem().Kind() != reflect.Slice {
		panic("dest must be slice")
	}

	filter := bson.D{}
	if v, ok := c.cacheStore.Load("builder"); ok && v.(bool) {
		filter = c.filter
	}

	val, ok := c.cacheStore.Load("table")
	if !ok || val == nil {
		panic("model must be fill first")
	}

	st := reflect.ValueOf(val).MethodByName("TableName")
	if !st.IsValid() {
		panic("model not implement TableName")
	}
	stt := st.Call([]reflect.Value{})
	tableName := stt[0].String()

	db := c.svc.Collection(tableName)

	opts := options.Find()
	if c.limit > 0 || c.offset > 0 {
		opts = opts.SetSkip(int64(c.offset)).SetLimit(int64(c.limit))
	}

	cursor, err := db.Find(c.ctx, filter, opts)
	if err != nil {
		panic("mongodb ERR: " + err.Error())
	}

	var results []bson.M
	if err = cursor.All(context.Background(), &results); err != nil {
		panic("mongodb ERR: " + err.Error())
	}

	datas := reflect.ValueOf(dest)
	datas = datas.Elem()
	data := datas.Type().Elem()

	fieldName := map[string]int{}
	for i := 0; i < data.NumField(); i++ {
		structTag := data.Field(i)
		tag := structTag.Tag

		val, ok := tag.Lookup("json")
		if ok {
			key := strings.Split(val, ",")[0]
			fieldName[key] = i
		}
	}

	for _, result := range results {
		output, err := json.MarshalIndent(result, "", "    ")
		if err != nil {
			panic("mongodb ERR: " + err.Error())
		}

		interfaceItem := reflect.New(data).Elem().Interface()
		err = json.Unmarshal(output, &interfaceItem)
		if err != nil {
			panic("mongodb ERR: " + err.Error())
		}

		if _, ok := interfaceItem.(map[string]interface{}); !ok {
			continue
		}

		newItem := reflect.New(data).Elem()
		for k, v := range interfaceItem.(map[string]interface{}) {
			val := reflect.ValueOf(v)
			structField := newItem.Field(fieldName[k])
			if structField.Type() == val.Type() {
				structField.Set(val)
				continue
			}

			if val.Type().ConvertibleTo(structField.Type()) {
				structField.Set(val.Convert(structField.Type()))
			}

			timeFormat := reflect.ValueOf(time.Time{}).Type()
			if structField.Type() == timeFormat {
				if val.Kind() == reflect.String {
					t, err := time.Parse(time.RFC3339Nano, v.(string))
					if err == nil {
						val = reflect.ValueOf(t)
						structField.Set(val)
						continue
					}
				}
			}
		}

		datas.Set(reflect.Append(datas, newItem))
	}

}
