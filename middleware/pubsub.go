package middleware

import (
	"context"
	"errors"

	"cloud.google.com/go/pubsub"
)

const (
	GOBER   = `gober`
	HORDE   = `horde`
	HYDRA   = `hydra`
	ALGO    = `algo`
	GENESIS = `genesis`
)

var IsAllowedAtributes map[string]bool = map[string]bool{
	HORDE: true,
}

func PubSubMiddleware(ctx context.Context, msg *pubsub.Message) error {
	if val, ok := msg.Attributes["source"]; ok {
		if !IsAllowedAtributes[val] {
			return errors.New("Pubsub atribut only allow")
		}
	}

	return nil
}
