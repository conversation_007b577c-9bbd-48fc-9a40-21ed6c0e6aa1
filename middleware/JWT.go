package middleware

import (
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/Lionparcel/horde/config"
	"google.golang.org/api/idtoken"
	"google.golang.org/api/option"

	"github.com/Lionparcel/go-lptool/v2/lptoken"
	"github.com/dgrijalva/jwt-go"
	"github.com/labstack/echo"
)

// JWTClaims data structure for claims
type JWTClaims struct {
	AccountID   int64  `json:"account_id"`
	AccountName string `json:"account_name"`
	AccountRole string `json:"role_id"`
	PartnerType string `json:"partner_type"`
	PartnerID   int    `json:"partner_id"`
	PartnerCode string `json:"partner_code"`
	PartnerName string `json:"partner_name"`
	ClientID    int    `json:"client_id"`
	ClientName  string `json:"client_name"`
	ClientCode  string `json:"client_code"`

	Username string `json:"username"`
	Super    bool   `json:"super"`
	Type     string `json:"type"`
	jwt.StandardClaims
	AccountRoleName string `json:"account_role_name"`
}

// JSONFailed ...
type JSONFailed struct {
	Success bool   `json:"success"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type JWTClaimsPubSub struct {
	Email           string `json:"email"`
	EmailVerified   bool   `json:"email_verified"`
	AuthorizedParty string `json:"azp"`
	jwt.StandardClaims
}

// VerifyExpiresAt function, will override original VerifyExpiresAt function
func (c *JWTClaims) VerifyExpiresAt(cmp int64, req bool) bool {
	var leeway int64 = 60 // one minutes
	return c.StandardClaims.VerifyExpiresAt(cmp-leeway, req)
}

// JWTVerify function to verify json web token
func JWTVerify(rsaPublicKey *rsa.PublicKey, mustAuthorized bool, accountType []string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {

			if os.Getenv("NO_TOKEN") == "1" {
				return next(c)
			}

			req := c.Request()
			header := req.Header
			auth := header.Get("Authorization")

			claims, err := lptoken.AuthenticateJWT(rsaPublicKey, auth, accountType)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, err.Error())
			}

			if claims == nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "authorization is invalid")
			}

			c.Set("token", claims.Token)
			c.Set("tokenStr", claims.TokenStr)
			c.Set("accountID", claims.AccountID)
			c.Set("accountName", claims.AccountName)
			c.Set("accountRoleName", claims.AccountRoleName)
			c.Set("partnerType", claims.PartnerType)
			c.Set("partnerID", claims.PartnerID)
			c.Set("partnerCode", claims.PartnerCode)
			c.Set("partnerName", claims.PartnerName)
			c.Set("clientID", claims.ClientID)
			c.Set("clientCode", claims.ClientCode)
			c.Set("clientName", claims.ClientName)
			c.Set("type", claims.Type)
			c.Set("kID", claims.KID)
			c.Set("username", claims.Username)

			return next(c)
		}
	}
}

func VerifyToken() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			response := new(JSONFailed)

			tokenStr := strings.Replace(c.Request().Header.Get("Authorization"), "Bearer ", "", -1)
			if len(tokenStr) == 0 {
				response.Success = false
				response.Message = "Not Found Authorization"
				response.Code = http.StatusUnauthorized

				return c.JSON(http.StatusNonAuthoritativeInfo, response)
			}

			claims := jwt.MapClaims{}
			jwt.ParseWithClaims(tokenStr, claims, func(token *jwt.Token) (interface{}, error) {
				return []byte("secret"), nil
			})

			// do something with decoded claims
			if claims["authorised"] == false && claims["email"] == "" {
				response.Success = false
				response.Message = "Unauthorized"
				response.Code = http.StatusUnauthorized

				return c.JSON(http.StatusNonAuthoritativeInfo, response)
			}
			return next(c)
		}
	}
}

// JWTVerify function to verify json web token
func JWTVerifyGcpPubSub(cfg config.Config, mustAuthorized bool) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {

			req := c.Request()
			header := req.Header
			auth := header.Get("Authorization")

			if len(auth) <= 0 {
				return echo.NewHTTPError(http.StatusUnauthorized, "authorization is empty")
			}

			splitToken := strings.Split(auth, " ")
			if len(splitToken) < 2 {
				return echo.NewHTTPError(http.StatusUnauthorized, "authorization is empty")
			}

			if splitToken[0] != "Bearer" {
				return echo.NewHTTPError(http.StatusUnauthorized, "authorization is invalid")
			}

			tokenStr := splitToken[1]

			v, err := idtoken.NewValidator(req.Context(), option.WithHTTPClient(http.DefaultClient))
			if err != nil {
				return echo.NewHTTPError(http.StatusBadRequest, "Unable to create validator")
			}

			scheme := req.URL.Scheme
			if scheme == `` {
				scheme = `http`
			}

			payload, err := v.Validate(req.Context(), tokenStr, "")
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, err.Error())
			}

			b, err := json.Marshal(payload.Claims)
			if err != nil {
				return echo.NewHTTPError(http.StatusBadRequest, err.Error())
			}

			token := &JWTClaimsPubSub{}
			if err := json.Unmarshal(b, token); err == nil {
				c.Set("token", token)
				c.Set("tokenStr", tokenStr)
				c.Set("email", token.Email)
				c.Set("emailVerified", token.EmailVerified)
				c.Set("authorizedParty", token.AuthorizedParty)

				googleCredential := cfg.GoogleCredential()

				if token.Issuer != "accounts.google.com" && token.Issuer != "https://accounts.google.com" {
					return echo.NewHTTPError(http.StatusBadRequest, "Wrong issuer")
				}

				if !token.EmailVerified || token.Email != googleCredential.ClientEmail {
					return echo.NewHTTPError(http.StatusBadRequest, "Unexpected service account identity")
				}

				return next(c)
			} else {
				return echo.NewHTTPError(http.StatusUnauthorized, fmt.Sprintf("Token Parsing Error: %s", err.Error()))
			}
		}
	}
}
